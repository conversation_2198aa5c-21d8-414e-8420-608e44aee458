app/code/Magento/InventoryBundleProductIndexer/Indexer/SelectBuilder.php
app/code/Magento/InventoryConfigurableProductIndexer/Indexer/SelectBuilder.php
app/code/Magento/InventoryCatalogAdminUi/Controller/Adminhtml/Source/BulkAssignPost.php
app/code/Magento/InventoryBundleProductIndexer/Indexer/SourceItem/SourceItemIndexer.php
app/code/Magento/InventoryShippingAdminUi/Ui/DataProvider/GetSourcesByOrderIdSkuAndQty.php
app/code/Magento/InventoryBundleProductIndexer/Indexer/SourceItem/SiblingSkuListInStockProvider.php
app/code/Magento/InventoryInStorePickupWebapiExtension/Model/Rest/Swagger/Generator.php
app/code/Magento/InventoryAdminUi/Controller/Adminhtml/Source/MassDisable.php
app/code/Magento/InventoryBundleProductIndexer/Indexer/StockIndexer.php
app/code/Magento/InventoryBundleProductIndexer/Indexer/SelectBuilder.php
app/code/Magento/InventoryConfigurableProductIndexer/Indexer/SourceItem/SiblingSkuListInStockProvider.php
