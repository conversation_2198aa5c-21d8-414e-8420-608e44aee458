<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
return [
    'admin_assert'                                => 'Magento\Backend',
    'authorization_role'                          => 'Magento\Authorization',
    'authorization_rule'                          => 'Magento\Authorization',
    'admin_user'                                  => 'Magento\User',
    'adminnotification_inbox'                     => 'Magento\AdminNotification',
    'catalog_category_entity_datetime'            => 'Magento\Catalog',
    'catalog_category_entity_decimal'             => 'Magento\Catalog',
    'catalog_category_entity_int'                 => 'Magento\Catalog',
    'catalog_category_entity_text'                => 'Magento\Catalog',
    'catalog_category_entity_varchar'             => 'Magento\Catalog',
    'catalog_product_entity_datetime'             => 'Magento\Catalog',
    'catalog_product_entity_decimal'              => 'Magento\Catalog',
    'catalog_product_entity_gallery'              => 'Magento\Catalog',
    'catalog_product_entity_int'                  => 'Magento\Catalog',
    'catalog_product_entity_text'                 => 'Magento\Catalog',
    'catalog_product_entity_varchar'              => 'Magento\Catalog',
    'catalog_product_bundle_option'               => 'Magento\Bundle',
    'catalog_product_index_price_bundle_opt_idx'  => 'Magento\Bundle',
    'catalog_product_index_price_bundle_opt_tmp'  => 'Magento\Bundle',
    'catalog_product_bundle_option_value'         => 'Magento\Bundle',
    'catalog_product_bundle_price_index'          => 'Magento\Bundle',
    'catalog_product_index_price_bundle_idx'      => 'Magento\Bundle',
    'catalog_product_index_price_bundle_tmp'      => 'Magento\Bundle',
    'catalog_product_bundle_selection'            => 'Magento\Bundle',
    'catalog_product_index_price_bundle_sel_idx'  => 'Magento\Bundle',
    'catalog_product_index_price_bundle_sel_tmp'  => 'Magento\Bundle',
    'catalog_product_bundle_selection_price'      => 'Magento\Bundle',
    'catalog_product_bundle_stock_index'          => 'Magento\Bundle',
    'captcha_log'                                 => 'Magento\Captcha',
    'catalog_category_entity'                     => 'Magento\Catalog',
    'catalog_category_flat'                       => 'Magento\Catalog',
    'catalog_category_product'                    => 'Magento\Catalog',
    'catalog_category_product_index'              => 'Magento\Catalog',
    'catalog_compare_item'                        => 'Magento\Catalog',
    'catalog_eav_attribute'                       => 'Magento\Catalog',
    'catalog_product_entity'                      => 'Magento\Catalog',
    'catalog_product_entity_media_gallery'        => 'Magento\Catalog',
    'catalog_product_entity_media_gallery_value'  => 'Magento\Catalog',
    'catalog_product_entity_tier_price'           => 'Magento\Catalog',
    'catalog_product_index_eav_decimal_idx'       => 'Magento\Catalog',
    'catalog_product_index_eav_decimal_tmp'       => 'Magento\Catalog',
    'catalog_product_index_eav_idx'               => 'Magento\Catalog',
    'catalog_product_index_eav_tmp'               => 'Magento\Catalog',
    'catalog_product_flat'                        => 'Magento\Catalog',
    'catalog_product_index_eav'                   => 'Magento\Catalog',
    'catalog_product_index_eav_decimal'           => 'Magento\Catalog',
    'catalog_product_index_price'                 => 'Magento\Catalog',
    'catalog_product_index_tier_price'            => 'Magento\Catalog',
    'catalog_product_index_website'               => 'Magento\Catalog',
    'catalog_product_link'                        => 'Magento\Catalog',
    'catalog_product_link_attribute'              => 'Magento\Catalog',
    'catalog_product_link_attribute_decimal'      => 'Magento\Catalog',
    'catalog_product_link_attribute_int'          => 'Magento\Catalog',
    'catalog_product_link_attribute_varchar'      => 'Magento\Catalog',
    'catalog_product_link_type'                   => 'Magento\Catalog',
    'catalog_product_option'                      => 'Magento\Catalog',
    'catalog_product_option_price'                => 'Magento\Catalog',
    'catalog_product_option_title'                => 'Magento\Catalog',
    'catalog_product_option_type_price'           => 'Magento\Catalog',
    'catalog_product_option_type_title'           => 'Magento\Catalog',
    'catalog_product_option_type_value'           => 'Magento\Catalog',
    'catalog_product_index_price_cfg_opt_agr_idx' => 'Magento\Catalog',
    'catalog_product_index_price_cfg_opt_agr_tmp' => 'Magento\Catalog',
    'catalog_product_index_price_cfg_opt_idx'     => 'Magento\Catalog',
    'catalog_product_index_price_cfg_opt_tmp'     => 'Magento\Catalog',
    'catalog_product_index_price_final_idx'       => 'Magento\Catalog',
    'catalog_product_index_price_final_tmp'       => 'Magento\Catalog',
    'catalog_product_index_price_idx'             => 'Magento\Catalog',
    'catalog_product_index_price_opt_agr_idx'     => 'Magento\Catalog',
    'catalog_product_index_price_opt_agr_tmp'     => 'Magento\Catalog',
    'catalog_product_index_price_opt_idx'         => 'Magento\Catalog',
    'catalog_product_index_price_opt_tmp'         => 'Magento\Catalog',
    'catalog_product_index_price_tmp'             => 'Magento\Catalog',
    'catalog_product_relation'                    => 'Magento\Catalog',
    'catalog_product_super_attribute'             => 'Magento\Catalog',
    'catalog_product_super_attribute_label'       => 'Magento\Catalog',
    'catalog_product_super_attribute_pricing'     => 'Magento\Catalog',
    'catalog_product_super_link'                  => 'Magento\Catalog',
    'catalog_product_website'                     => 'Magento\Catalog',
    'cataloginventory_stock'                      => 'Magento\CatalogInventory',
    'cataloginventory_stock_item'                 => 'Magento\CatalogInventory',
    'cataloginventory_stock_status'               => 'Magento\CatalogInventory',
    'cataloginventory_stock_status_idx'           => 'Magento\CatalogInventory',
    'cataloginventory_stock_status_tmp'           => 'Magento\CatalogInventory',
    'catalogrule_customer_group'                  => 'Magento\CatalogRule',
    'catalogrule'                                 => 'Magento\CatalogRule',
    'catalogrule_group_website'                   => 'Magento\CatalogRule',
    'catalogrule_product'                         => 'Magento\CatalogRule',
    'catalogrule_product_price'                   => 'Magento\CatalogRule',
    'catalogrule_website'                         => 'Magento\CatalogRule',
    'catalogsearch_fulltext'                      => 'Magento\CatalogSearch',
    'catalogsearch_result'                        => 'Magento\CatalogSearch',
    'search_query'                                => 'Magento\Search',
    'checkout_agreement'                          => 'Magento\Checkout',
    'checkout_agreement_store'                    => 'Magento\Checkout',
    'cms_block'                                   => 'Magento\Cms',
    'cms_block_store'                             => 'Magento\Cms',
    'cms_page'                                    => 'Magento\Cms',
    'cms_page_store'                              => 'Magento\Cms',
    'core_config_data'                            => 'Magento\Config',
    'design_change'                               => 'Magento\Theme',
    'media_storage_directory_storage'             => 'Magento\MediaStorage',
    'email_template'                              => 'Magento\Email',
    'media_storage_file_storage'                  => 'Magento\MediaStorage',
    'store'                                       => 'Magento\Store',
    'store_group'                                 => 'Magento\Store',
    'store_website'                               => 'Magento\Store',
    'cron_schedule'                               => 'Magento\Cron',
    'customer_address_entity'                     => 'Magento\Customer',
    'customer_group'                              => 'Magento\Customer',
    'customer_eav_attribute'                      => 'Magento\Customer',
    'customer_eav_attribute_website'              => 'Magento\Customer',
    'customer_entity'                             => 'Magento\Customer',
    'customer_form_attribute'                     => 'Magento\Customer',
    'customer_address_entity_datetime'            => 'Magento\Customer',
    'customer_address_entity_decimal'             => 'Magento\Customer',
    'customer_address_entity_int'                 => 'Magento\Customer',
    'customer_address_entity_text'                => 'Magento\Customer',
    'customer_address_entity_varchar'             => 'Magento\Customer',
    'customer_entity_datetime'                    => 'Magento\Customer',
    'customer_entity_decimal'                     => 'Magento\Customer',
    'customer_entity_int'                         => 'Magento\Customer',
    'customer_entity_text'                        => 'Magento\Customer',
    'customer_entity_varchar'                     => 'Magento\Customer',
    'customer_visitor'                            => 'Magento\Customer',
    'directory_country'                           => 'Magento\Directory',
    'directory_country_format'                    => 'Magento\Directory',
    'directory_country_name'                      => 'Magento\Directory',
    'directory_country_region'                    => 'Magento\Directory',
    'directory_country_region_name'               => 'Magento\Directory',
    'directory_currency_rate'                     => 'Magento\Directory',
    'downloadable_link'                           => 'Magento\Downloadable',
    'downloadable_link_price'                     => 'Magento\Downloadable',
    'downloadable_link_purchased'                 => 'Magento\Downloadable',
    'downloadable_link_purchased_item'            => 'Magento\Downloadable',
    'downloadable_link_title'                     => 'Magento\Downloadable',
    'catalog_product_index_price_downlod_idx'     => 'Magento\Downloadable',
    'catalog_product_index_price_downlod_tmp'     => 'Magento\Downloadable',
    'downloadable_sample'                         => 'Magento\Downloadable',
    'downloadable_sample_title'                   => 'Magento\Downloadable',
    'eav_attribute'                               => 'Magento\Eav',
    'eav_attribute_group'                         => 'Magento\Eav',
    'eav_attribute_label'                         => 'Magento\Eav',
    'eav_attribute_option'                        => 'Magento\Eav',
    'eav_attribute_option_value'                  => 'Magento\Eav',
    'eav_attribute_set'                           => 'Magento\Eav',
    'eav_entity'                                  => 'Magento\Eav',
    'eav_entity_attribute'                        => 'Magento\Eav',
    'eav_entity_attribute_source_table'           => 'Magento\Eav',
    'eav_entity_store'                            => 'Magento\Eav',
    'eav_entity_type'                             => 'Magento\Eav',
    'eav_form_element'                            => 'Magento\Eav',
    'eav_form_fieldset'                           => 'Magento\Eav',
    'eav_form_fieldset_label'                     => 'Magento\Eav',
    'eav_form_type'                               => 'Magento\Eav',
    'eav_form_type_entity'                        => 'Magento\Eav',
    'eav_entity_datetime'                         => 'Magento\Eav',
    'eav_entity_decimal'                          => 'Magento\Eav',
    'eav_entity_int'                              => 'Magento\Eav',
    'eav_entity_text'                             => 'Magento\Eav',
    'eav_entity_varchar'                          => 'Magento\Eav',
    'find_feed_import_codes'                      => 'Find\Feed',
    'gift_message'                                => 'Magento\GiftMessage',
    'googleoptimizer_code'                        => 'Magento\GoogleOptimizer',
    'importexport_importdata'                     => 'Magento\ImportExport',
    'integration'                                 => 'Magento\Integration',
    'layout_link'                                 => 'Magento\Widget',
    'layout_update'                               => 'Magento\Widget',
    'log_customer'                                => 'Magento\Log',
    'log_quote'                                   => 'Magento\Log',
    'log_summary'                                 => 'Magento\Log',
    'log_summary_type'                            => 'Magento\Log',
    'log_url_info'                                => 'Magento\Log',
    'log_url'                                     => 'Magento\Log',
    'log_visitor'                                 => 'Magento\Log',
    'log_visitor_info'                            => 'Magento\Log',
    'log_visitor_online'                          => 'Magento\Log',
    'newsletter_problem'                          => 'Magento\Newsletter',
    'newsletter_queue'                            => 'Magento\Newsletter',
    'newsletter_queue_link'                       => 'Magento\Newsletter',
    'newsletter_queue_store_link'                 => 'Magento\Newsletter',
    'newsletter_subscriber'                       => 'Magento\Newsletter',
    'newsletter_template'                         => 'Magento\Newsletter',
    'oauth_consumer'                              => 'Magento\Integration',
    'oauth_nonce'                                 => 'Magento\Integration',
    'oauth_token'                                 => 'Magento\Integration',
    'authorizenet_debug'                          => 'Magento\Authorizenet',
    'admin_passwords'                             => 'Magento\User',
    'paypal_cert'                                 => 'Magento\Paypal',
    'paypal_payment_transaction'                  => 'Magento\Paypal',
    'paypal_settlement_report'                    => 'Magento\Paypal',
    'paypal_settlement_report_row'                => 'Magento\Paypal',
    'persistent_session'                          => 'Magento\Persistent',
    'product_alert_price'                         => 'Magento\ProductAlert',
    'product_alert_stock'                         => 'Magento\ProductAlert',
    'rating'                                      => 'Magento\Review',
    'rating_entity'                               => 'Magento\Review',
    'rating_option'                               => 'Magento\Review',
    'rating_option_vote'                          => 'Magento\Review',
    'rating_store'                                => 'Magento\Review',
    'rating_title'                                => 'Magento\Review',
    'rating_option_vote_aggregated'               => 'Magento\Review',
    'report_compared_product_index'               => 'Magento\Reports',
    'report_event'                                => 'Magento\Reports',
    'report_event_types'                          => 'Magento\Reports',
    'report_viewed_product_index'                 => 'Magento\Reports',
    'review'                                      => 'Magento\Review',
    'review_entity_summary'                       => 'Magento\Review',
    'review_detail'                               => 'Magento\Review',
    'review_entity'                               => 'Magento\Review',
    'review_status'                               => 'Magento\Review',
    'review_store'                                => 'Magento\Review',
    'sales_bestsellers_aggregated_daily'          => 'Magento\Sales',
    'sales_bestsellers_aggregated_monthly'        => 'Magento\Sales',
    'sales_bestsellers_aggregated_yearly'         => 'Magento\Sales',
    'paypal_billing_agreement'                    => 'Magento\Paypal',
    'paypal_billing_agreement_order'              => 'Magento\Paypal',
    'sales_creditmemo'                            => 'Magento\Sales',
    'sales_creditmemo_comment'                    => 'Magento\Sales',
    'sales_creditmemo_grid'                       => 'Magento\Sales',
    'sales_creditmemo_item'                       => 'Magento\Sales',
    'sales_invoice'                               => 'Magento\Sales',
    'sales_invoice_comment'                       => 'Magento\Sales',
    'sales_invoice_grid'                          => 'Magento\Sales',
    'sales_invoice_item'                          => 'Magento\Sales',
    'sales_invoiced_aggregated'                   => 'Magento\Sales',
    'sales_invoiced_aggregated_order'             => 'Magento\Sales',
    'sales_order'                                 => 'Magento\Sales',
    'sales_order_address'                         => 'Magento\Sales',
    'sales_order_aggregated_created'              => 'Magento\Sales',
    'sales_order_aggregated_updated'              => 'Magento\Sales',
    'sales_order_grid'                            => 'Magento\Sales',
    'sales_order_item'                            => 'Magento\Sales',
    'sales_order_item_option'                     => 'Magento\Sales',
    'sales_order_payment'                         => 'Magento\Sales',
    'sales_order_status'                          => 'Magento\Sales',
    'sales_order_status_history'                  => 'Magento\Sales',
    'sales_order_status_label'                    => 'Magento\Sales',
    'sales_order_status_state'                    => 'Magento\Sales',
    'sales_order_tax'                             => 'Magento\Tax',
    'sales_payment_transaction'                   => 'Magento\Sales',
    'quote'                                       => 'Magento\Quote',
    'quote_address'                               => 'Magento\Quote',
    'quote_address_item'                          => 'Magento\Quote',
    'quote_shipping_rate'                         => 'Magento\Quote',
    'quote_item'                                  => 'Magento\Quote',
    'quote_item_option'                           => 'Magento\Quote',
    'quote_payment'                               => 'Magento\Quote',
    'sales_refunded_aggregated'                   => 'Magento\Sales',
    'sales_refunded_aggregated_order'             => 'Magento\Sales',
    'sales_shipment'                              => 'Magento\Sales',
    'sales_shipment_comment'                      => 'Magento\Sales',
    'sales_shipment_grid'                         => 'Magento\Sales',
    'sales_shipment_item'                         => 'Magento\Sales',
    'sales_shipment_track'                        => 'Magento\Sales',
    'sales_shipping_aggregated'                   => 'Magento\Sales',
    'sales_shipping_aggregated_order'             => 'Magento\Sales',
    'quote_entity'                                => 'Magento\Quote',
    'quote_temp'                                  => 'Magento\Quote',
    'salesrule_coupon'                            => 'Magento\SalesRule',
    'coupon_aggregated'                           => 'Magento\SalesRule',
    'coupon_aggregated_order'                     => 'Magento\SalesRule',
    'coupon_aggregated_updated'                   => 'Magento\SalesRule',
    'salesrule_coupon_usage'                      => 'Magento\SalesRule',
    'salesrule_website'                           => 'Magento\SalesRule',
    'salesrule_label'                             => 'Magento\SalesRule',
    'salesrule_product_attribute'                 => 'Magento\SalesRule',
    'salesrule'                                   => 'Magento\SalesRule',
    'salesrule_customer'                          => 'Magento\SalesRule',
    'salesrule_customer_group'                    => 'Magento\SalesRule',
    'sendfriend_log'                              => 'Magento\sendfriend',
    'shipping_tablerate'                          => 'Magento\shipping',
    'sitemap'                                     => 'Magento\Sitemap',
    'social_facebook_actions'                     => 'Social\Facebook',
    'sales_order_tax_item'                        => 'Magento\Tax',
    'tax_calculation'                             => 'Magento\Tax',
    'tax_calculation_rate'                        => 'Magento\Tax',
    'tax_calculation_rate_title'                  => 'Magento\Tax',
    'tax_calculation_rule'                        => 'Magento\Tax',
    'tax_class'                                   => 'Magento\Tax',
    'tax_order_aggregated_created'                => 'Magento\Tax',
    'tax_order_aggregated_updated'                => 'Magento\Tax',
    'translation'                                 => 'Magento\Translation',
    'weee_tax'                                    => 'Magento\Weee',
    'widget'                                      => 'Magento\Widget',
    'widget_instance'                             => 'Magento\Widget',
    'widget_instance_page'                        => 'Magento\Widget',
    'widget_instance_page_layout'                 => 'Magento\Widget',
    'wishlist_item'                               => 'Magento\Wishlist',
    'wishlist_item_option'                        => 'Magento\Wishlist',
    'wishlist'                                    => 'Magento\Wishlist',
    'admin_system_messages'                       => 'Magento\AdminNotification',
    'theme'                                       => 'Magento\Theme',
    'theme_files'                                 => 'Magento\Theme',
    'variable'                                    => 'Magento\Variable',
    'variable_value'                              => 'Magento\Variable',
    'job_queue'                                   => 'Magento\Queue',
    'catalogsearch_recommendations'               => 'Magento\AdvancedSearch',
];
