{"env": {"amd": true, "browser": true, "jasmine": true, "jquery": true, "prototypejs": true, "node": true}, "rules": {"eqeqeq": [2, "smart"], "guard-for-in": 2, "lines-around-comment": [2, {"beforeBlockComment": true, "allowBlockStart": true, "allowObjectStart": true}], "max-depth": [2, 2], "max-len": [2, 120, 4], "max-nested-callbacks": [2, 3], "newline-after-var": 2, "no-alert": 2, "no-array-constructor": 2, "no-caller": 2, "no-catch-shadow": 2, "no-cond-assign": 2, "no-constant-condition": 2, "no-debugger": 2, "no-else-return": 2, "no-eval": 2, "no-ex-assign": 2, "no-extend-native": 2, "no-extra-bind": 2, "no-extra-boolean-cast": 2, "no-extra-parens": 2, "no-extra-semi": 2, "no-fallthrough": 2, "no-floating-decimal": 2, "no-func-assign": 2, "no-implied-eval": 2, "no-inner-declarations": 2, "no-invalid-regexp": 2, "no-lone-blocks": 2, "no-lonely-if": 2, "no-loop-func": 2, "no-multi-str": 2, "no-native-reassign": 2, "no-negated-in-lhs": 2, "no-new-object": 2, "no-proto": 2, "no-redeclare": 2, "no-regex-spaces": 2, "no-return-assign": 2, "no-self-compare": 2, "no-shadow": 2, "no-undef": 2, "no-undef-init": 2, "no-unreachable": 2, "no-unused-vars": [2, {"args": "after-used", "vars": "all", "varsIgnorePattern": "config"}], "no-use-before-define": 2, "no-with": 2, "operator-assignment": [2, "always"], "radix": 2, "semi": [2, "always"], "semi-spacing": 2, "strict": ["error", "function"], "use-isnan": 2, "valid-typeof": 2, "vars-on-top": 2}}