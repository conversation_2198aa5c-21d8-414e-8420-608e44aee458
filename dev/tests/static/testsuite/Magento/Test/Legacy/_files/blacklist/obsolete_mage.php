<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
return [
    'dev/tests/static/testsuite/Magento/Test/Legacy/ObsoleteCodeTest.php',
    'dev/tests/static/testsuite/Magento/Test/Integrity/ClassesTest.php',
    'dev/tests/static/testsuite/Magento/Test/Legacy/_files/*obsolete*.php',
    'dev/tests/static/testsuite/Magento/Test/Legacy/_files/obsolete_classes.php',
    'lib/internal/Magento/Framework/ObjectManager/Test/Unit/Factory/CompiledTest.php',
    'dev/tests/integration/testsuite/Magento/Indexer/Model/Config/_files/result.php',
    'lib/internal/Magento/Framework/Encryption/Test/Unit/EncryptorTest.php',
    'lib/internal/Magento/Framework/Encryption/Test/Unit/CryptTest.php',
    'setup/src/Zend/Mvc/Controller/LazyControllerAbstractFactory.php',
    'app/code/Magento/CatalogInventory/Block/Adminhtml/Form/Field/Stock.php',
];
