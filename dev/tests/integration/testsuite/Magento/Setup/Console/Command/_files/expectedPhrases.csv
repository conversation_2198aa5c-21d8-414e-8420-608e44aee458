"simple text","simple text"
"simple text with 1 string literal placeholder %1","simple text with 1 string literal placeholder %1"
"simple text with 1 variable placeholder %1","simple text with 1 variable placeholder %1"
"simple text with multiple placeholders %1 %2","simple text with multiple placeholders %1 %2"
"first part second part","first part second part"
"first part second part third part","first part second part third part"
"first part second part with one string literal placeholder %1","first part second part with one string literal placeholder %1"
"first part of concat second part with one variable placeholder %1","first part of concat second part with one variable placeholder %1"
"first part of concat second part with two placeholders %1, %2","first part of concat second part with two placeholders %1, %2"
"first part of concat second part third part with one placeholder %1","first part of concat second part third part with one placeholder %1"
"first part of concat second part third part with two placeholders %1, %2","first part of concat second part third part with two placeholders %1, %2"
"string with escaped 'single quotes'","string with escaped 'single quotes'"
"string with placeholder in escaped single quotes '%1'","string with placeholder in escaped single quotes '%1'"
"string with ""double quotes""","string with ""double quotes"""
"string with placeholder in double quotes ""%1""","string with placeholder in double quotes ""%1"""
"string with 'single quotes'","string with 'single quotes'"
"string with placeholder in single quotes '%1'","string with placeholder in single quotes '%1'"
"string with escaped ""double quotes""","string with escaped ""double quotes"""
"string with placeholder in escaped double quotes ""%1""","string with placeholder in escaped double quotes ""%1"""
"string that's got an unclosed single quote in it","string that's got an unclosed single quote in it"
"Thank you for your order from %store_name.","Thank you for your order from %store_name."
"Our hours are <span class=""no-link"">%store_hours</span>.","Our hours are <span class=""no-link"">%store_hours</span>."
"Translation inside if directive","Translation inside if directive"
"Test di text","Test di text"
