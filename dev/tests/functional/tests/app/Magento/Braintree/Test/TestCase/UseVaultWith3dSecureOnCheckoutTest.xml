<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Braintree\Test\TestCase\UseVaultWith3dSecureOnCheckoutTest" summary="Use saved for Braintree Credit card with 3D secure enabled.">
        <variation name="UseVaultWith3dSecureOnCheckoutTestVariation1" summary="Use saved for Braintree credit card on checkout with 3D Secure" ticketId="MAGETWO-55310">
            <data name="description" xsi:type="string">Use saved for Braintree credit card on checkout</data>
            <data name="products/0" xsi:type="string">catalogProductSimple::product_10_dollar</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="shippingAddress/dataset" xsi:type="string">US_address_1_without_email</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">braintree</data>
            <data name="paymentForm" xsi:type="string">braintree</data>
            <data name="vault/method" xsi:type="string">braintree_cc_vault</data>
            <data name="creditCard/dataset" xsi:type="string">visa_braintree_3dsecure</data>
            <data name="paymentInformation" xsi:type="array">
                <item name="Liability Shifted" xsi:type="string">1</item>
                <item name="Liability Shift Possible" xsi:type="string">1</item>
            </data>
            <data name="secure3d/dataset" xsi:type="string">secure3d_braintree</data>
            <data name="creditCardSave" xsi:type="string">Yes</data>
            <data name="configData" xsi:type="string">braintree, braintree_use_vault, braintree_3d_secure</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">test_type:3rd_party_test, severity:S1</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Cancel, Send Email, Hold, Invoice, Ship</data>
            <constraint name="Magento\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Magento\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Magento\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Magento\Braintree\Test\Constraint\Assert3dSecureInfoIsPresent" />
        </variation>
    </testCase>
</config>
