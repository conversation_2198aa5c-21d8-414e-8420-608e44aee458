<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="queue"
             module="Magento_Newsletter"
             type="flat"
             entity_type="newsletter_queue"
             collection="Magento\Newsletter\Model\ResourceModel\Queue\Collection"
             identifier="queue_id"
             repository_class="Magento\Newsletter\Test\Repository\Queue"
             handler_interface="Magento\Newsletter\Test\Handler\Template\QueueInterface"
             class="Magento\Newsletter\Test\Fixture\Queue">
        <field name="queue_id" is_required="1" />
        <field name="template_id" is_required="1" />
        <field name="newsletter_type" is_required="" />
        <field name="newsletter_text" is_required="" />
        <field name="newsletter_styles" is_required="" />
        <field name="newsletter_subject" is_required="" />
        <field name="newsletter_sender_name" is_required="" />
        <field name="newsletter_sender_email" is_required="" />
        <field name="queue_status" is_required="1" />
        <field name="queue_start_at" is_required="" source="Magento\Backend\Test\Fixture\Source\Date" />
        <field name="queue_finish_at" is_required="" source="Magento\Backend\Test\Fixture\Source\Date" />
        <field name="stores" is_required="1" source="Magento\Newsletter\Test\Fixture\Queue\Stores"/>
    </fixture>
</config>
