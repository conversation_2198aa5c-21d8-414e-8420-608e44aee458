<?xml version="1.0" ?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Magento\Customer\Test\Repository\Customer">
        <dataset name="johndoe_subscribed_to_newsletter">
            <field name="firstname" xsi:type="string">John</field>
            <field name="lastname" xsi:type="string">Doe</field>
            <field name="email" xsi:type="string"><EMAIL></field>
            <field name="is_subscribed" xsi:type="string">1</field>
            <field name="password" xsi:type="string">123123^q</field>
            <field name="password_confirmation" xsi:type="string">123123^q</field>
            <field name="dob" xsi:type="string">01/01/1990</field>
            <field name="gender" xsi:type="string">Male</field>
            <field name="group_id" xsi:type="array">
                <item name="dataset" xsi:type="string">General</item>
            </field>
        </dataset>
    </repository>
</config>
