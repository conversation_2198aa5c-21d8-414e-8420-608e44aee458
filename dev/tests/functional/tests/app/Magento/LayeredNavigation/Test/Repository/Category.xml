<?xml version="1.0" ?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Magento\Catalog\Test\Repository\Category">
        <dataset name="default_non_anchored_subcategory">
            <field name="name" xsi:type="string">DefaultSubcategory%isolation%</field>
            <field name="url_key" xsi:type="string">default-subcategory-%isolation%</field>
            <field name="parent_id" xsi:type="array">
                <item name="dataset" xsi:type="string">default_category</item>
            </field>
            <field name="is_active" xsi:type="string">Yes</field>
            <field name="is_anchor" xsi:type="string">No</field>
            <field name="include_in_menu" xsi:type="string">Yes</field>
        </dataset>

        <dataset name="default_second_level_anchored_subcategory">
            <field name="name" xsi:type="string">DefaultSubcategory%isolation%</field>
            <field name="url_key" xsi:type="string">default-subcategory-%isolation%</field>
            <field name="parent_id" xsi:type="array">
                <item name="dataset" xsi:type="string">default_non_anchored_subcategory</item>
            </field>
            <field name="is_active" xsi:type="string">Yes</field>
            <field name="is_anchor" xsi:type="string">Yes</field>
            <field name="include_in_menu" xsi:type="string">Yes</field>
        </dataset>

        <dataset name="default_third_level_non_anchored_subcategory">
            <field name="name" xsi:type="string">DefaultSubcategory%isolation%</field>
            <field name="url_key" xsi:type="string">default-subcategory-%isolation%</field>
            <field name="parent_id" xsi:type="array">
                <item name="dataset" xsi:type="string">default_second_level_anchored_subcategory</item>
            </field>
            <field name="is_active" xsi:type="string">Yes</field>
            <field name="is_anchor" xsi:type="string">No</field>
            <field name="include_in_menu" xsi:type="string">Yes</field>
        </dataset>

        <dataset name="default_fourth_level_anchored_subcategory">
            <field name="name" xsi:type="string">DefaultSubcategory%isolation%</field>
            <field name="url_key" xsi:type="string">default-subcategory-%isolation%</field>
            <field name="parent_id" xsi:type="array">
                <item name="dataset" xsi:type="string">default_third_level_non_anchored_subcategory</item>
            </field>
            <field name="is_active" xsi:type="string">Yes</field>
            <field name="is_anchor" xsi:type="string">Yes</field>
            <field name="include_in_menu" xsi:type="string">Yes</field>
        </dataset>
    </repository>
</config>
