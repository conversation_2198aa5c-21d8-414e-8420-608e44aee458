<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Review\Test\Constraint\AssertProductRatingSuccessSaveMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductRatingSuccessDeleteMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductRatingInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductRatingNotInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductRatingInProductPage">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductRatingNotInProductPage">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertReviewSuccessSaveMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertReviewCreationSuccessMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductRatingOnReviewPage">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductReviewMassActionSuccessMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductReviewMassActionSuccessDeleteMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductReviewBackendSuccessSaveMessage">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductReviewNotOnProductPage">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Review\Test\Constraint\AssertProductReviewOnProductPage">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
</config>
