<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="giftMessage"
             module="Magento_GiftMessage"
             type="flat"
             entity_type="gift_message"
             collection="Magento\GiftMessage\Model\ResourceModel\Message\Collection"
             identifier="gift_message_id"
             repository_class="Magento\GiftMessage\Test\Repository\GiftMessage"
             class="Magento\GiftMessage\Test\Fixture\GiftMessage">
        <field name="gift_message_id" is_required="1" />
        <field name="customer_id" is_required="" />
        <field name="sender" is_required="" />
        <field name="recipient" is_required="" />
        <field name="message" is_required="" />
        <field name="allow_gift_options" />
        <field name="allow_gift_messages_for_order" />
        <field name="allow_gift_options_for_items" />
        <field name="items" source="Magento\GiftMessage\Test\Fixture\GiftMessage\Items" />
    </fixture>
</config>
