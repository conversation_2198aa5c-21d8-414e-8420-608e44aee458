<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="synonymGroup"
             module="Magento_Search"
             type="flat"
             entity_type="synonym_group"
             collection="Magento\Search\Model\ResourceModel\Block\Grid\Collection"
             handler_interface="Magento\Search\Test\Handler\SynonymGroup\SynonymGroupInterface"
             repository_class="Magento\Search\Test\Repository\SynonymGroup" class="Magento\Search\Test\Fixture\SynonymGroup">
        <field name="group_id" is_required="1"/>
        <field name="synonyms" is_required="0" />
        <field name="scope_id" is_required="0" source="Magento\Search\Test\Fixture\SynonymGroup\ScopeId" />
        <field name="mergeOnConflict" is_required="0" />
    </fixture>
</config>
