<?xml version="1.0" ?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Magento\Downloadable\Test\Repository\DownloadableProduct\CheckoutData">
        <dataset name="downloadable_default">
            <field name="options" xsi:type="array">
                <item name="links" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string">link_0</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">282.43</item>
                <item name="subtotal" xsi:type="string">282.43</item>
            </field>
        </dataset>

        <dataset name="downloadable_one_dollar_product_with_no_separated_link">
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">3</item>
                <item name="subtotal" xsi:type="string">3</item>
            </field>
        </dataset>

        <dataset name="downloadable_with_two_separately_links">
            <field name="options" xsi:type="array">
                <item name="links" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string">link_0</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">22.43</item>
                <item name="subtotal" xsi:type="string">22.43</item>
            </field>
        </dataset>

        <dataset name="downloadable_with_two_bought_links">
            <field name="options" xsi:type="array">
                <item name="links" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string">link_0</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                    <item name="1" xsi:type="array">
                        <item name="label" xsi:type="string">link_1</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">23</item>
                <item name="subtotal" xsi:type="string">23</item>
            </field>
        </dataset>

        <dataset name="downloadable_update_mini_shopping_cart">
            <field name="options" xsi:type="array">
                <item name="links" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string">link_1</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">25.43</item>
                <item name="subtotal" xsi:type="string">25.43</item>
            </field>
        </dataset>

        <dataset name="downloadable_one_custom_option_and_downloadable_link">
            <field name="options" xsi:type="array">
                <item name="custom_options" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_0</item>
                        <item name="value" xsi:type="string">option_key_0</item>
                    </item>
                </item>
                <item name="links" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string">link_0</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                </item>
            </field>
        </dataset>

        <dataset name="downloadable_one_dollar_product_with_separated_link">
            <field name="options" xsi:type="array">
                <item name="links" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string">link_0</item>
                        <item name="value" xsi:type="string">Yes</item>
                    </item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">3</item>
                <item name="subtotal" xsi:type="string">3</item>
            </field>
        </dataset>
    </repository>
</config>
