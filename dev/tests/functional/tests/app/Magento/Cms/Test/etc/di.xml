<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Cms\Test\Constraint\AssertCmsBlockDeleteMessage">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsBlockInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">S2</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsBlockNotInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">S3</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsBlockNotOnCategoryPage">
        <arguments>
            <argument name="severity" xsi:type="string">S3</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsBlockOnCategoryPage">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsBlockSuccessSaveMessage">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageDeleteMessage">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageDisabledOnFrontend">
        <arguments>
            <argument name="severity" xsi:type="string">S2</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageDuplicateErrorMessage">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageForm">
        <arguments>
            <argument name="severity" xsi:type="string">S2</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">S2</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageNotInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">S3</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageOnFrontend">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPagePreview">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertCmsPageSuccessSaveMessage">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Cms\Test\Constraint\AssertUrlRewriteCmsPageRedirect">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
</config>
