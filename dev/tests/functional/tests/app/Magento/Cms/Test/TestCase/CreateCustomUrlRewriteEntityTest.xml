<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\UrlRewrite\Test\TestCase\CreateCustomUrlRewriteEntityTest" summary="Create Custom URL Rewrites" ticketId="MAGETWO-25474">
        <variation name="CreateCustomUrlRewriteEntityTestVariation3">
            <data name="tag" xsi:type="string">severity:S1, mftf_migrated:yes</data>
            <data name="urlRewrite/data/entity_type" xsi:type="string">Custom</data>
            <data name="urlRewrite/data/store_id" xsi:type="string">Main Website/Main Website Store/Default Store View</data>
            <data name="urlRewrite/data/target_path/entity" xsi:type="string">cms/page/view/page_id/%cmsPage::default%</data>
            <data name="urlRewrite/data/request_path" xsi:type="string">cms_page_request_path%isolation%</data>
            <data name="urlRewrite/data/redirect_type" xsi:type="string">No</data>
            <data name="urlRewrite/data/description" xsi:type="string">test description_full path</data>
            <constraint name="Magento\UrlRewrite\Test\Constraint\AssertUrlRewriteSaveMessage" />
            <constraint name="Magento\UrlRewrite\Test\Constraint\AssertUrlRewriteCustomRedirect" />
        </variation>
        <variation name="CreateCustomUrlRewriteEntityTestVariation4">
            <data name="tag" xsi:type="string">severity:S1, mftf_migrated:yes</data>
            <data name="urlRewrite/data/entity_type" xsi:type="string">Custom</data>
            <data name="urlRewrite/data/store_id" xsi:type="string">Main Website/Main Website Store/Default Store View</data>
            <data name="urlRewrite/data/target_path/entity" xsi:type="string">cms/page/view/page_id/%cmsPage::default%</data>
            <data name="urlRewrite/data/request_path" xsi:type="string">cms_page_request_path%isolation%</data>
            <data name="urlRewrite/data/redirect_type" xsi:type="string">Temporary (302)</data>
            <data name="urlRewrite/data/description" xsi:type="string">test description_full path</data>
            <constraint name="Magento\UrlRewrite\Test\Constraint\AssertUrlRewriteSaveMessage" />
            <constraint name="Magento\UrlRewrite\Test\Constraint\AssertUrlRewriteCustomRedirect" />
        </variation>
    </testCase>
</config>
