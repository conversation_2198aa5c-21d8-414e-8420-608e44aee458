<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Ui\Test\TestCase\GridFilteringTest" summary="Grid UI Component Filtering" ticketId="MAGETWO-41329">
        <variation name="CmsPageGridFiltering">
            <data name="tag" xsi:type="string">severity:S3</data>
            <data name="description" xsi:type="string">Verify cms page grid filtering</data>
            <data name="itemsCount" xsi:type="string">2</data>
            <data name="fixtureName" xsi:type="string">cmsPage</data>
            <data name="fixtureDataSet" xsi:type="string">default</data>
            <data name="filters" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">:title</item>
                    <item name="identifier" xsi:type="string">:identifier</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="title" xsi:type="string">:title</item>
                    <item name="page_id_from" xsi:type="string">:page_id</item>
                </item>
            </data>
            <data name="pageClass" xsi:type="string">Magento\Cms\Test\Page\Adminhtml\CmsPageIndex</data>
            <data name="gridRetriever" xsi:type="string">getCmsPageGridBlock</data>
            <data name="idGetter" xsi:type="string">getPageId</data>
            <constraint name="\Magento\Ui\Test\Constraint\AssertGridFiltering"/>
        </variation>
        <variation name="CmsBlockGridFiltering">
            <data name="tag" xsi:type="string">severity:S3</data>
            <data name="description" xsi:type="string">Verify cms block grid filtering</data>
            <data name="itemsCount" xsi:type="string">2</data>
            <data name="fixtureName" xsi:type="string">cmsBlock</data>
            <data name="fixtureDataSet" xsi:type="string">default</data>
            <data name="filters" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">:title</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="block_id_from" xsi:type="string">:block_id</item>
                </item>
            </data>
            <data name="pageClass" xsi:type="string">Magento\Cms\Test\Page\Adminhtml\CmsBlockIndex</data>
            <data name="gridRetriever" xsi:type="string">getCmsBlockGrid</data>
            <data name="idGetter" xsi:type="string">getBlockId</data>
            <constraint name="Magento\Ui\Test\Constraint\AssertGridFiltering"/>
        </variation>
    </testCase>
</config>
