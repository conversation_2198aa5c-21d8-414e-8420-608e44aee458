<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/etc/pages.xsd">
    <page name="WidgetInstanceEdit" area="Adminhtml" mca="admin/widget_instance/edit/" module="Magento_Widget">
        <block name="widgetForm" class="Magento\Widget\Test\Block\Adminhtml\Widget\Instance\Edit\WidgetForm" locator="[id='page:main-container']" strategy="css selector" />
        <block name="pageActionsBlock" class="Magento\Backend\Test\Block\FormPageActions" locator=".page-main-actions" strategy="css selector" />
        <block name="templateBlock" class="Magento\Backend\Test\Block\Template" locator="body" strategy="css selector" />
        <block name="modalBlock" class="Magento\Ui\Test\Block\Adminhtml\Modal" locator="._show[data-role=modal]" strategy="css selector"/>
    </page>
</config>
