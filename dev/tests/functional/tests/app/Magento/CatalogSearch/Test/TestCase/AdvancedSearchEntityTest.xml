<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\CatalogSearch\Test\TestCase\AdvancedSearchEntityTest" summary="Use Advanced Search" ticketId="MAGETWO-24729">
        <variation name="AdvancedSearchEntityTestVariation1" summary="Use Advanced Search to Find the Product" ticketId="MAGETWO-12421">
            <data name="tag" xsi:type="string">test_type:acceptance_test, test_type:extended_acceptance_test, mftf_migrated:yes</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/name" xsi:type="string">abc_dfj</data>
            <data name="productSearch/data/sku" xsi:type="string">abc_dfj</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation2">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by name</data>
            <data name="products/simple_1" xsi:type="string">-</data>
            <data name="products/simple_2" xsi:type="string">Yes</data>
            <data name="productSearch/data/name" xsi:type="string">adc_123</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation3">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by partial name</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/name" xsi:type="string">abc</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation4">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by sku</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/sku" xsi:type="string">abc_dfj</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation5">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by partial sku</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/sku" xsi:type="string">abc</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation6">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by partial sku and description</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/sku" xsi:type="string">abc</data>
            <data name="productSearch/data/description" xsi:type="string">adc_full</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation7">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by description</data>
            <data name="products/simple_1" xsi:type="string">-</data>
            <data name="products/simple_2" xsi:type="string">Yes</data>
            <data name="productSearch/data/description" xsi:type="string">dfj_full</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation8">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by short description</data>
            <data name="products/simple_1" xsi:type="string">-</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/short_description" xsi:type="string">dfj_short</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation9">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by partial short description</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/short_description" xsi:type="string">abc_short</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation10">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by price to</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">Yes</data>
            <data name="productSearch/data/price/value/price_to" xsi:type="string">100</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation11">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by price from and price to</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/price/value/price_from" xsi:type="string">50</data>
            <data name="productSearch/data/price/value/price_to" xsi:type="string">50</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation12">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by name, sku, description, short description, price from and price to</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/name" xsi:type="string">abc_dfj</data>
            <data name="productSearch/data/sku" xsi:type="string">abc_dfj</data>
            <data name="productSearch/data/description" xsi:type="string">adc_Full</data>
            <data name="productSearch/data/short_description" xsi:type="string">abc_short</data>
            <data name="productSearch/data/price/value/price_from" xsi:type="string">49</data>
            <data name="productSearch/data/price/value/price_to" xsi:type="string">500</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation13">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Search product in advanced search by name, sku, description, short description, price from and price to</data>
            <data name="products/simple_1" xsi:type="string">Yes</data>
            <data name="products/simple_2" xsi:type="string">-</data>
            <data name="productSearch/data/name" xsi:type="string">abc_dfj</data>
            <data name="productSearch/data/sku" xsi:type="string">abc_dfj</data>
            <data name="productSearch/data/description" xsi:type="string">adc_Full</data>
            <data name="productSearch/data/short_description" xsi:type="string">abc_short</data>
            <data name="productSearch/data/price/value/price_from" xsi:type="string">49</data>
            <data name="productSearch/data/price/value/price_to" xsi:type="string">50</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchProductsResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation14">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Negative product search</data>
            <data name="productSearch/data/name" xsi:type="string">Negative_product_search</data>
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchNoResult" />
        </variation>
        <variation name="AdvancedSearchEntityTestVariation15" summary="Do Advanced Search without entering data" ticketId="MAGETWO-14859">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="issue" xsi:type="string">MAGETWO-18537: "Enter a search term and try again." error message is missed in Advanced Search</data>
            <data name="productSearch/data/name" xsi:type="string" />
            <constraint name="Magento\CatalogSearch\Test\Constraint\AssertAdvancedSearchEmptyTerm" />
        </variation>
    </testCase>
</config>
