<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Backend\Test\Constraint\AssertBackendPageIsAvailable">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Test\Constraint\AssertHttpsUsedOnBackend">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Test\Constraint\AssertHttpsUsedOnFrontend">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Test\Constraint\AssertStoreCanBeLocalized">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Test\Constraint\AssertLocaleCodeVisibility">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Test\Constraint\AssertDeveloperSectionVisibility">
        <arguments>
            <argument name="severity" xsi:type="string">S1</argument>
        </arguments>
    </type>
</config>
