<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Backend\Test\TestCase\VerifyInterfaceLocaleTest" summary="Check that interface locale dropdown has correct options" ticketId="MAGETWO-64920, MAGETWO-64921">
        <variation name="CorrectOptionsOfInterfaceLocales" summary="Check that interface locale dropdown has correct options" ticketId="MAGETWO-64920, MAGETWO-64921">
            <data name="tag" xsi:type="string">severity:S1</data>
        </variation>
    </testCase>
</config>
