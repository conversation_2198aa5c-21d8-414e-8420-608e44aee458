<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/etc/pages.xsd">
  <page name="StoreNew" area="Adminhtml" mca="admin/system_store/newStore" module="Magento_Backend">
    <block name="formPageActions" class="Magento\Backend\Test\Block\System\Store\FormPageActions" locator=".page-main-actions" strategy="css selector"/>
    <block name="storeForm" class="Magento\Backend\Test\Block\System\Store\Edit\Form\StoreForm" locator="[id='page:main-container']" strategy="css selector"/>
    <block name="messagesBlock" class="Magento\Backend\Test\Block\Messages" locator="#messages" strategy="css selector"/>
    <block name="modalBlock" class="Magento\Ui\Test\Block\Adminhtml\Modal" locator="._show[data-role=modal]" strategy="css selector"/>
  </page>
</config>
