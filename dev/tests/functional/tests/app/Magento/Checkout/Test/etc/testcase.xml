<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/TestCase/etc/testcase.xsd">
    <scenario name="OnePageCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="estimateShippingAndTax" />
        <step name="estimateShippingAndTax" module="Magento_Checkout" next="clickProceedToCheckout" />
        <step name="clickProceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="refreshPage" />
        <step name="refreshPage" module="Magento_Checkout" next="placeOrder" />
        <step name="placeOrder" module="Magento_Checkout" next="createCustomerAccount" />
        <step name="createCustomerAccount" module="Magento_Checkout" />
    </scenario>
    <scenario name="OnePageCheckoutOfflinePaymentMethodsTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="estimateShippingAndTax" />
        <step name="estimateShippingAndTax" module="Magento_Checkout" next="clickProceedToCheckout" />
        <step name="clickProceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="refreshPage" />
        <step name="refreshPage" module="Magento_Checkout" next="placeOrder" />
        <step name="placeOrder" module="Magento_Checkout" next="createCustomerAccount" />
        <step name="createCustomerAccount" module="Magento_Checkout" next="createShipment" />
        <step name="createShipment" module="Magento_Sales"/>
    </scenario>
    <scenario name="OnePageCheckoutJsValidationTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="ProceedToCheckout" />
        <step name="ProceedToCheckout" module="Magento_Checkout" />
    </scenario>
    <scenario name="OnePageCheckoutDeclinedTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="clickPlaceOrderButton" />
        <step name="clickPlaceOrderButton" module="Magento_Checkout" />
    </scenario>
    <scenario name="OnePageCheckoutFromMiniShoppingCartTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckoutFromMiniShoppingCart" />
        <step name="proceedToCheckoutFromMiniShoppingCart" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="placeOrder" />
        <step name="placeOrder" module="Magento_Checkout" next="createCustomerAccount" />
        <step name="createCustomerAccount" module="Magento_Checkout" />
    </scenario>
    <scenario name="VerifyPaymentMethodOnCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckoutFromMiniShoppingCart" />
        <step name="proceedToCheckoutFromMiniShoppingCart" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" />
    </scenario>
    <scenario name="ShoppingCartPagerTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="mergePreconditionProducts" />
        <step name="mergePreconditionProducts" module="Magento_Catalog" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="removeProductsFromTheCart" />
        <step name="removeProductsFromTheCart" module="Magento_Checkout" />
    </scenario>
    <scenario name="EditShippingAddressOnePageCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="createProducts"/>
        <step name="createProducts" module="Magento_Catalog" next="loginCustomerOnFrontend"/>
        <step name="loginCustomerOnFrontend" module="Magento_Customer" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Magento_Checkout" next="addNewShippingAddress"/>
        <step name="addNewShippingAddress" module="Magento_Checkout" next="editShippingAddress"/>
        <step name="editShippingAddress" module="Magento_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Magento_Checkout" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="refreshPage" />
        <step name="refreshPage" module="Magento_Checkout" next="placeOrder" />
        <step name="placeOrder" module="Magento_Checkout"/>
    </scenario>
</config>
