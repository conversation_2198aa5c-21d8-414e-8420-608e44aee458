<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Security\Test\TestCase\LockAdminUserWhenEditingIntegrationTest" summary="Lock admin user after entering incorrect password while editing integration">
        <variation name="LockAdminUserWhenCreatingNewIntegrationTestVariation1">
            <data name="configData" xsi:type="string">user_lockout_failures</data>
            <data name="tag" xsi:type="string">severity:S2</data>
            <data name="customAdmin/dataset" xsi:type="string">custom_admin_with_default_role</data>
            <data name="initintegration/dataset" xsi:type="string">default_active</data>
            <data name="integration/data/name" xsi:type="string">Integration%isolation%</data>
            <data name="integration/data/current_password" xsi:type="string">incorrect password</data>
            <data name="attempts" xsi:type="string">4</data>
            <constraint name="Magento\Security\Test\Constraint\AssertUserIsLocked" />
        </variation>
    </testCase>
</config>
