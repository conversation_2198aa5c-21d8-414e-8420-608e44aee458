<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Weee\Test\TestCase\CreateTaxWithFptTest" summary="Apply FPT to Different Type Prices" ticketId="MAGETWO-29551">
        <variation name="CreateTaxWithFptTestVariation1" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="tag" xsi:type="string">to_maintain:yes</data>
            <data name="description" xsi:type="string">Check not taxed FPT display set to Excluding, Description and Including FPT on product with custom option catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_cat_excl_disc_on_excl</data>
            <data name="productData" xsi:type="string">with_custom_option_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">70.00</data>
            <data name="prices/fpt_category" xsi:type="string">10.00</data>
            <data name="prices/fpt_total_category" xsi:type="string">80.00</data>
            <data name="prices/product_page_price" xsi:type="string">100.00</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.00</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_price" xsi:type="string">100.00</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">100.00</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/grand_total" xsi:type="string">123.66</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation2" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="tag" xsi:type="string">to_maintain:yes</data>
            <data name="description" xsi:type="string">Check not taxed FPT display set to Including FPT and Description on product with custom option catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_cat_excl_disc_on_incl, display_including_tax</data>
            <data name="productData" xsi:type="string">with_custom_option_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">75.78</data>
            <data name="prices/fpt_category" xsi:type="string">10.00</data>
            <data name="prices/fpt_total_category" xsi:type="string">85.78</data>
            <data name="prices/product_page_price" xsi:type="string">108.25</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.00</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">118.25</data>
            <data name="prices/cart_item_price" xsi:type="string">108.25</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">118.25</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">108.25</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">118.25</data>
            <data name="prices/grand_total" xsi:type="string">123.66</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">115.00</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">123.66</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation3" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="tag" xsi:type="string">stable:no</data>
            <data name="description" xsi:type="string">Check not taxed FPT display set to Excluding, Description and Including FPT on product with special price catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_cat_excl_disc_on_incl, display_including_tax</data>
            <data name="productData" xsi:type="string">with_special_price_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">108.25</data>
            <data name="prices/fpt_category" xsi:type="string">10.00</data>
            <data name="prices/fpt_total_category" xsi:type="string">118.25</data>
            <data name="prices/product_page_price" xsi:type="string">108.25</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.00</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">118.25</data>
            <data name="prices/cart_item_price" xsi:type="string">108.25</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">118.25</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">108.25</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">118.25</data>
            <data name="prices/grand_total" xsi:type="string">123.66</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">115.00</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">123.66</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation4" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="tag" xsi:type="string">stable:no</data>
            <data name="description" xsi:type="string">Check not taxed FPT display set to Including FPT and Description on product with special price catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_cat_excl_disc_on_excl</data>
            <data name="productData" xsi:type="string">with_special_price_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">100.00</data>
            <data name="prices/fpt_category" xsi:type="string">10.00</data>
            <data name="prices/fpt_total_category" xsi:type="string">110.00</data>
            <data name="prices/product_page_price" xsi:type="string">100.00</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.00</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_price" xsi:type="string">100.00</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">100.00</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/grand_total" xsi:type="string">123.66</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation5" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="tag" xsi:type="string">to_maintain:yes</data>
            <data name="description" xsi:type="string">Check taxed FPT display set to Excluding, Description and Including FPT on product with custom option catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_excl_disc_on_excl</data>
            <data name="productData" xsi:type="string">with_custom_option_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">70.00</data>
            <data name="prices/fpt_category" xsi:type="string">10.00</data>
            <data name="prices/fpt_total_category" xsi:type="string">80.00</data>
            <data name="prices/product_page_price" xsi:type="string">100.00</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.00</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_price" xsi:type="string">100.00</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">100.00</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/grand_total" xsi:type="string">124.49</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation6" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="description" xsi:type="string">Check taxed FPT display set to Including FPT and Description on product with custom option catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_excl_disc_on_incl, display_including_tax</data>
            <data name="productData" xsi:type="string">with_custom_option_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">86.60</data>
            <data name="prices/fpt_category" xsi:type="string">10.83</data>
            <data name="prices/product_page_price" xsi:type="string">119.08</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.83</data>
            <data name="prices/cart_item_price" xsi:type="string">119.08</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.83</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">119.08</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.83</data>
            <data name="prices/grand_total" xsi:type="string">124.49</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">115.00</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">124.49</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation7" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="description" xsi:type="string">Check taxed FPT display set to Excluding, Description and Including FPT on product with special price catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_excl_disc_on_incl, display_including_tax</data>
            <data name="productData" xsi:type="string">with_special_price_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">119.08</data>
            <data name="prices/fpt_category" xsi:type="string">10.83</data>
            <data name="prices/product_page_price" xsi:type="string">119.08</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.83</data>
            <data name="prices/cart_item_price" xsi:type="string">119.08</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.83</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">119.08</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.83</data>
            <data name="prices/grand_total" xsi:type="string">124.49</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">115.00</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">124.49</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation8" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="description" xsi:type="string">Check taxed FPT display set to Including FPT and Description on product with special price catalog price Excluding Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_excl_disc_on_excl</data>
            <data name="productData" xsi:type="string">with_special_price_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">100.00</data>
            <data name="prices/fpt_category" xsi:type="string">10.00</data>
            <data name="prices/fpt_total_category" xsi:type="string">110.00</data>
            <data name="prices/product_page_price" xsi:type="string">100.00</data>
            <data name="prices/product_page_fpt" xsi:type="string">10.00</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_price" xsi:type="string">100.00</data>
            <data name="prices/cart_item_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">100.00</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">10.00</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">110.00</data>
            <data name="prices/grand_total" xsi:type="string">124.49</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation9" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="description" xsi:type="string">Check taxed FPT display set to Excluding, Description and Including FPT on product with special price and catalog price Including Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_incl_disc_on_excl</data>
            <data name="productData" xsi:type="string">with_special_price_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">92.38</data>
            <data name="prices/fpt_category" xsi:type="string">9.24</data>
            <data name="prices/fpt_total_category" xsi:type="string">101.62</data>
            <data name="prices/product_page_price" xsi:type="string">92.38</data>
            <data name="prices/product_page_fpt" xsi:type="string">9.24</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">101.62</data>
            <data name="prices/cart_item_price" xsi:type="string">92.38</data>
            <data name="prices/cart_item_fpt" xsi:type="string">9.24</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">101.62</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">92.38</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">9.24</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">101.62</data>
            <data name="prices/grand_total" xsi:type="string">115.00</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation10" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="description" xsi:type="string">Check taxed FPT display set to Including FPT and Description on product with special price and catalog price Including Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_incl_disc_on_incl</data>
            <data name="productData" xsi:type="string">with_special_price_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">101.62</data>
            <data name="prices/fpt_category" xsi:type="string">9.24</data>
            <data name="prices/product_page_price" xsi:type="string">101.62</data>
            <data name="prices/product_page_fpt" xsi:type="string">9.24</data>
            <data name="prices/cart_item_price" xsi:type="string">101.62</data>
            <data name="prices/cart_item_fpt" xsi:type="string">9.24</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">101.62</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">9.24</data>
            <data name="prices/grand_total" xsi:type="string">115.00</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
        <variation name="CreateTaxWithFptTestVariation11" firstConstraint="Magento\Weee\Test\Constraint\AssertFptApplied" method="test">
            <data name="issue" xsi:type="string">MAGETWO-44968: FPT Final price includes tax on custom option, when display is set to excluding tax</data>
            <data name="description" xsi:type="string">Check taxed FPT display set to Excluding, Description and Including FPT on product with custom option and catalog price Including Tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods,tax_with_fpt_taxed_cat_incl_disc_on_excl</data>
            <data name="productData" xsi:type="string">with_custom_option_and_fpt</data>
            <data name="prices/category_price" xsi:type="string">64.67</data>
            <data name="prices/fpt_category" xsi:type="string">9.24</data>
            <data name="prices/fpt_total_category" xsi:type="string">73.90</data>
            <data name="prices/product_page_price" xsi:type="string">92.38</data>
            <data name="prices/product_page_fpt" xsi:type="string">9.24</data>
            <data name="prices/product_page_fpt_total" xsi:type="string">101.62</data>
            <data name="prices/cart_item_price" xsi:type="string">92.38</data>
            <data name="prices/cart_item_fpt" xsi:type="string">9.24</data>
            <data name="prices/cart_item_fpt_total" xsi:type="string">101.62</data>
            <data name="prices/cart_item_subtotal" xsi:type="string">92.38</data>
            <data name="prices/cart_item_subtotal_fpt" xsi:type="string">9.24</data>
            <data name="prices/cart_item_subtotal_fpt_total" xsi:type="string">101.62</data>
            <data name="prices/grand_total" xsi:type="string">115.00</data>
            <data name="prices/total_fpt" xsi:type="string">10.00</data>
            <constraint name="Magento\Weee\Test\Constraint\AssertFptApplied" />
        </variation>
    </testCase>
</config>
