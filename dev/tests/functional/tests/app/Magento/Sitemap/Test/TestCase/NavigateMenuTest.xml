<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Backend\Test\TestCase\NavigateMenuTest">
        <variation name="NavigateMenuTest85">
            <data name="tag" xsi:type="string">severity:S2, mftf_migrated:yes</data>
            <data name="menuItem" xsi:type="string">Marketing > Site Map</data>
            <data name="pageTitle" xsi:type="string">Site Map</data>
            <constraint name="Magento\Backend\Test\Constraint\AssertBackendPageIsAvailable"/>
        </variation>
    </testCase>
</config>
