<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/TestCase/etc/testcase.xsd">
    <scenario name="InContextExpressOnePageCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="continueToPaypalInContext" />
        <step name="continueToPaypalInContext" module="Magento_Paypal" />
    </scenario>
    <scenario name="InContextExpressCheckoutFromShoppingCartTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="inContextCheckoutWithPaypalFromShoppingCart" />
        <step name="inContextCheckoutWithPaypalFromShoppingCart" module="Magento_Paypal" />
    </scenario>
    <scenario name="ExpressCheckoutFromProductPageTest" firstStep="createSandboxCustomer">
        <step name="createSandboxCustomer" module="Magento_Paypal" next="setupConfiguration" />
        <step name="setupConfiguration" module="Magento_Config" next="createProduct" />
        <step name="createProduct" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="checkoutWithPaypalFromProductPage" />
        <step name="checkoutWithPaypalFromProductPage" module="Magento_Paypal" next="continuePaypalCheckout" />
        <step name="continuePaypalCheckout" module="Magento_Paypal" next="expressCheckoutOrderPlace" />
        <step name="expressCheckoutOrderPlace" module="Magento_Paypal" />
    </scenario>
    <scenario name="ExpressCheckoutFromShoppingCartTest" firstStep="createSandboxCustomer">
        <step name="createSandboxCustomer" module="Magento_Paypal" next="setupConfiguration" />
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="checkoutWithPaypalFromShoppingCart" />
        <step name="checkoutWithPaypalFromShoppingCart" module="Magento_Paypal" next="continuePaypalCheckout" />
        <step name="continuePaypalCheckout" module="Magento_Paypal" next="expressCheckoutOrderPlace" />
        <step name="expressCheckoutOrderPlace" module="Magento_Paypal" />
    </scenario>
    <scenario name="ExpressCheckoutOnePageTest" firstStep="createSandboxCustomer">
        <step name="createSandboxCustomer" module="Magento_Paypal" next="setupConfiguration" />
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="continueToPaypal" />
        <step name="continueToPaypal" module="Magento_Paypal" next="continuePaypalCheckout" />
        <step name="continuePaypalCheckout" module="Magento_Paypal" next="getPlacedOrderId" />
        <step name="getPlacedOrderId" module="Magento_Checkout" />
    </scenario>
    <scenario name="ConflictResolutionTest" firstStep="checkExpressConfig">
        <step name="checkExpressConfig" module="Magento_Paypal" next="checkBraintreeConfig" />
        <step name="checkBraintreeConfig" module="Magento_Braintree" next="checkPaymentsAdvancedConfig" />
        <step name="checkPaymentsAdvancedConfig" module="Magento_Paypal" next="checkPaymentsProConfig" />
        <step name="checkPaymentsProConfig" module="Magento_Paypal" next="checkPayflowProConfig" />
        <step name="checkPayflowProConfig" module="Magento_Paypal" next="checkPayflowLinkConfig" />
        <step name="checkPayflowLinkConfig" module="Magento_Paypal" />
    </scenario>
    <scenario name="CreatePayFlowOrderBackendNegativeTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="openSalesOrders" />
        <step name="openSalesOrders" module="Magento_Sales" next="createNewOrder" />
        <step name="createNewOrder" module="Magento_Sales" next="selectCustomerOrder" />
        <step name="selectCustomerOrder" module="Magento_Sales" next="selectStore" />
        <step name="selectStore" module="Magento_Sales" next="addProducts" />
        <step name="addProducts" module="Magento_Sales" next="fillAccountInformation" />
        <step name="fillAccountInformation" module="Magento_Sales" next="fillBillingAddress" />
        <step name="fillBillingAddress" module="Magento_Sales" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Sales" next="selectShippingMethodForOrder" />
        <step name="selectShippingMethodForOrder" module="Magento_Sales" next="saveCreditCardOnBackend" />
        <step name="saveCreditCardOnBackend" module="Magento_Vault" next="submitOrderNegative" />
        <step name="submitOrderNegative" module="Magento_Sales" />
    </scenario>
    <scenario name="OnePageCheckoutHostedProTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="placeOrderWithHostedPro" />
        <step name="placeOrderWithHostedPro" module="Magento_Paypal" next="getPlacedOrderId" />
        <step name="getPlacedOrderId" module="Magento_Checkout" />
    </scenario>
    <scenario name="CloseSalesWithHostedProTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress"/>
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="placeOrderWithHostedPro" />
        <step name="placeOrderWithHostedPro" module="Magento_Paypal" next="getPlacedOrderId" />
        <step name="getPlacedOrderId" module="Magento_Checkout" next="createInvoice" />
        <step name="createInvoice" module="Magento_Sales" next="createShipment" />
        <step name="createShipment" module="Magento_Sales" />
    </scenario>
    <scenario name="OnePageCheckoutPaymentsAdvancedTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="placeOrderWithPaymentsAdvanced" />
        <step name="placeOrderWithPaymentsAdvanced" module="Magento_Paypal" next="getPlacedOrderId" />
        <step name="getPlacedOrderId" module="Magento_Checkout" />
    </scenario>
    <scenario name="OnePageCheckoutPayflowLinkTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="placeOrderWithPayflowLink" />
        <step name="placeOrderWithPayflowLink" module="Magento_Paypal" next="getPlacedOrderId" />
        <step name="getPlacedOrderId" module="Magento_Checkout" />
    </scenario>
    <scenario name="CreateOnlineCreditMemoPayflowLinkTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="proceedToCheckout" />
        <step name="proceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="placeOrderWithPayflowLink" />
        <step name="placeOrderWithPayflowLink" module="Magento_Paypal" next="getPlacedOrderId" />
        <step name="getPlacedOrderId" module="Magento_Checkout" next="createInvoice" />
        <step name="createInvoice" module="Magento_Sales" next="createOnlineCreditMemo" />
        <step name="createOnlineCreditMemo" module="Magento_Sales" />
    </scenario>
</config>
