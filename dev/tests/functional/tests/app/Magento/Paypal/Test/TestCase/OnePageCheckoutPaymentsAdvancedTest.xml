<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Paypal\Test\TestCase\OnePageCheckoutPaymentsAdvancedTest" summary="Guest Checkout using PayPal Payments Advanced and Flat Rate">
        <variation name="OnePageCheckoutPaymentsAdvancedVariation1" summary="Guest Checkout using PayPal Payments Advanced and Flat Rate" ticketId="MAGETWO-12991">
            <data name="products/0" xsi:type="string">catalogProductSimple::product_10_dollar</data>
            <data name="products/1" xsi:type="string">configurableProduct::with_one_option</data>
            <data name="products/2" xsi:type="string">bundleProduct::bundle_fixed_100_dollar_product</data>
            <data name="taxRule" xsi:type="string">us_ca_ny_rule</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="shippingAddress/dataset" xsi:type="string">US_address_1_without_email</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">payflow_advanced</data>
            <data name="prices" xsi:type="array">
                <item name="grandTotal" xsi:type="string">145.98</item>
            </data>
            <data name="creditCard/dataset" xsi:type="string">visa_default</data>
            <data name="isVaultPresent" xsi:type="boolean">false</data>
            <data name="configData" xsi:type="string">payments_advanced</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">test_type:3rd_party_test, severity:S0</data>
            <constraint name="Magento\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Magento\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Magento\Sales\Test\Constraint\AssertAuthorizationInCommentsHistory" />
        </variation>
    </testCase>
</config>
