<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Bundle\Test\TestCase\CreateBundleDynamicProductEntityTest" summary="Create Bundle Product" ticketId="MAGETWO-24118">
        <variation name="CreateBundleProductEntityTestVariation1">
            <data name="tag" xsi:type="string">stable:no</data>
            <data name="description" xsi:type="string">Create default bundle with dynamic options</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">BundleProduct %isolation%</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/description" xsi:type="string">Bundle Product Dynamic Required</data>
            <data name="product/data/stock_data/use_config_manage_stock" xsi:type="string">No</data>
            <data name="product/data/stock_data/manage_stock" xsi:type="string">No</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInGrid" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleItemsOnProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation2">
            <data name="description" xsi:type="string">Create offline dynamic bundle with dynamic price and out of stock</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">BundleProduct %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">No</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/status" xsi:type="string">No</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/quantity_and_stock_status/is_in_stock" xsi:type="string">Out of Stock</data>
            <data name="product/data/weight_type" xsi:type="string">Yes</data>
            <data name="product/data/category" xsi:type="string">category_%isolation%</data>
            <data name="product/data/shipment_type" xsi:type="string">Separately</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/use_config_gift_message_available" xsi:type="string">No</data>
            <data name="product/data/gift_message_available" xsi:type="string">Yes</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductNotSearchableBySku" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation3">
            <data name="tag" xsi:type="string">test_type:extended_acceptance_test, to_maintain:yes</data>
            <data name="description" xsi:type="string">Create dynamic bundle with price randle and all types options</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">BundleProduct %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/status" xsi:type="string">Yes</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-200</data>
            <data name="product/data/quantity_and_stock_status/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/weight_type" xsi:type="string">Yes</data>
            <data name="product/data/category" xsi:type="string">category_%isolation%</data>
            <data name="product/data/description" xsi:type="string">Bundle Product Dynamic</data>
            <data name="product/data/price_view" xsi:type="string">Price Range</data>
            <data name="product/data/shipment_type" xsi:type="string">Together</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">all_types_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar|catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar|catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar|catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_all_types_bundle_options</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInGrid" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductForm" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSearchableBySku" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInStock" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleItemsOnProductPage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductVisibleInCategory" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceView" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceType" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation7" summary="Check bundle dynamic product with tier price">
            <data name="tag" xsi:type="string">to_maintain:yes</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">BundleProduct %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-50</data>
            <data name="product/data/weight_type" xsi:type="string">No</data>
            <data name="product/data/weight" xsi:type="string">10</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">custom_with_percentage_discount</data>
            <data name="product/data/price_view" xsi:type="string">As Low as</data>
            <data name="product/data/stock_data/use_config_manage_stock" xsi:type="string">No</data>
            <data name="product/data/stock_data/manage_stock" xsi:type="string">No</data>
            <data name="product/data/shipment_type" xsi:type="string">Together</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductVirtual::product_50_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <data name="product/data/visibility" xsi:type="string">Search</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInGrid" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductForm" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSearchableBySku" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleItemsOnProductPage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertTierPriceOnBundleProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation8">
            <data name="tag" xsi:type="string">to_maintain:yes</data>
            <data name="description" xsi:type="string">Create dynamic bundle with special price</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-8</data>
            <data name="product/data/special_price" xsi:type="string">20</data>
            <data name="product/data/special_from_date/pattern" xsi:type="string">m/d/y -1 day</data>
            <data name="product/data/special_to_date/pattern" xsi:type="string">m/d/y +3 days</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductSimple::product_40_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleInCategory" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation9">
            <data name="description" xsi:type="string">Create dynamic bundle with group price</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-40</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductSimple::product_40_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleInCategory" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceView" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceType" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation10">
            <data name="description" xsi:type="string">Create dynamic bundle</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-40</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductSimple::product_40_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleInCategory" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceView" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceType" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation11">
            <data name="issue" xsi:type="string">MAGETWO-52788: Dynamic Rows: support status being changed</data>
            <data name="description" xsi:type="string">Create fixed product with checkout first option</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Fixed %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">No</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_fixed_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">No</data>
            <data name="product/data/price/value" xsi:type="string">110</data>
            <data name="product/data/price/dataset" xsi:type="string">fixed-115</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">second</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductSimple::product_40_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_with_custom_options_1</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">drop_down_with_one_option_fixed_price</data>
            <data name="product/data/custom_options/import_products" xsi:type="string">catalogProductSimple::with_two_custom_option,catalogProductSimple::with_all_custom_option</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleInCategory" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceView" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceType" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertProductCustomOptionsOnBundleProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation13">
            <data name="description" xsi:type="string">Create default dynamic bundle</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductSimple::product_40_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation16" summary="Create Bundle (dynamic) Product and Assign it to the Category" ticketId="MAGETWO-12702">
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-560</data>
            <data name="product/data/category" xsi:type="string">category_%isolation%</data>
            <data name="product/data/shipment_type" xsi:type="string">Together</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">one_options_assigned_simple_big_qty</data>
            <data name="tag" xsi:type="string">test_type:acceptance_test, stable:no</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInGrid" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleInCategory" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation18" summary="Create dynamic bundle product with tier price for sub-item">
            <data name="tag" xsi:type="string">stable:no</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">BundleProduct %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-50</data>
            <data name="product/data/weight_type" xsi:type="string">No</data>
            <data name="product/data/weight" xsi:type="string">10</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">custom_with_percentage_discount</data>
            <data name="product/data/price_view" xsi:type="string">As Low as</data>
            <data name="product/data/stock_data/use_config_manage_stock" xsi:type="string">No</data>
            <data name="product/data/stock_data/manage_stock" xsi:type="string">No</data>
            <data name="product/data/shipment_type" xsi:type="string">Together</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::simple_with_tier_price,catalogProductVirtual::product_50_dollar</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <data name="product/data/visibility" xsi:type="string">Search</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductForm" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleItemsOnProductPage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertTierPriceOnBundleProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation19" summary="Create bundle with simple out of stock">
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">BundleProduct %isolation%</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-50</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::out_of_stock,catalogProductSimple::out_of_stock</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductOutOfStock" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation23" summary="Create Bundle (dynamic) Product with special price">
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-8</data>
            <data name="product/data/special_price" xsi:type="string">20</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/bundle_selections/products" xsi:type="string">catalogProductSimple::product_100_dollar,catalogProductSimple::product_40_dollar</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation24" summary="Create Bundle (dynamic) Product with One of the bundle options has special price">
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-18</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">dynamic_with_two_required_options_assigned_products_with_special_price</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
        </variation>
        <variation name="CreateBundleProductEntityTestVariation25" summary="Create Bundle (dynamic) Product with one require option with one item" ticketId="MAGETWO-59841">
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">Bundle Dynamic %isolation%</data>
            <data name="product/data/sku" xsi:type="string">sku_bundle_dynamic_%isolation%</data>
            <data name="product/data/price_type" xsi:type="string">Yes</data>
            <data name="product/data/category" xsi:type="string">category_%isolation%</data>
            <data name="product/data/shipment_type" xsi:type="string">Together</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">one_required_option_with_one_item</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">one_required_option_with_one_item</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceCalculatedOnProductPage" />
        </variation>
    </testCase>
</config>
