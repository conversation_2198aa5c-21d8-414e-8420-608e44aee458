<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Bundle\Test\TestCase\UpdateBundleProductEntityTest" summary="Update Bundle Product" ticketId="MAGETWO-26195">
        <variation name="UpdateBundleProductEntityTestVariation1">
            <data name="description" xsi:type="string">Update dynamic bundle product</data>
            <data name="originalProduct/dataset" xsi:type="string">bundle_dynamic_product</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">bundle_dynamic_%isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">No</data>
            <data name="product/data/sku" xsi:type="string">bundle_dynamic_%isolation%</data>
            <data name="product/data/price/dataset" xsi:type="string">dynamic-100</data>
            <data name="product/data/weight_type" xsi:type="string">No</data>
            <data name="product/data/weight" xsi:type="string">1</data>
            <data name="product/data/description" xsi:type="string">Bundle Product Fixed Required</data>
            <data name="product/data/bundle_shipment_type" xsi:type="string">Together</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_dynamic</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInGrid" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleItemsOnProductPage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductForm" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInStock" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceView" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceType" />
        </variation>
        <variation name="UpdateBundleProductEntityTestVariation2">
            <data name="description" xsi:type="string">Update fixed bundle product</data>
            <data name="originalProduct/dataset" xsi:type="string">bundle_fixed_product</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">bundle_fixed_%isolation%</data>
            <data name="product/data/sku_type" xsi:type="string">Yes</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price/dataset" xsi:type="string">fixed-756</data>
            <data name="product/data/weight_type" xsi:type="string">Yes</data>
            <data name="product/data/category_ids/dataset" xsi:type="string">default_subcategory</data>
            <data name="product/data/bundle_shipment_type" xsi:type="string">Separately</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">default_fixed</data>
            <data name="product/data/checkout_data/dataset" xsi:type="string">bundle_default</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInGrid" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleItemsOnProductPage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductForm" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductInStock" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductVisibleInCategory" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceView" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundlePriceType" />
        </variation>
        <variation name="UpdateBundleProductEntityTestVariation3" summary="Edit Bundle Product (Fixed Price)" ticketId="MAGETWO-12841">
            <data name="originalProduct/dataset" xsi:type="string">bundle_fixed_with_category</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">bundle_fixed_%isolation%</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price/value" xsi:type="string">120.00</data>
            <data name="product/data/price/dataset" xsi:type="string">bundle_fixed_with_category</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
        </variation>
        <variation name="UpdateBundleProductEntityTestVariation4" summary="Edit Bundle (dynamic) Product" ticketId="MAGETWO-12842">
            <data name="originalProduct/dataset" xsi:type="string">bundle_dynamic_with_category</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">bundle_dynamic_%isolation%</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price/dataset" xsi:type="string">bundle_dynamic_with_category</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
        </variation>
        <variation name="UpdateBundleProductEntityTestVariation5" summary="Update Bundle (fixed) Product with custom option">
            <data name="originalProduct/dataset" xsi:type="string">bundle_fixed_product</data>
            <data name="product/data/url_key" xsi:type="string">bundle-product-%isolation%</data>
            <data name="product/data/name" xsi:type="string">bundle_fixed_%isolation%</data>
            <data name="product/data/sku" xsi:type="string">bundle_sku_%isolation%</data>
            <data name="product/data/price/dataset" xsi:type="string">fixed-756-custom-options</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">drop_down_with_one_option_fixed_price</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductPage" />
        </variation>
        <variation name="UpdateBundleProductEntityTestVariation6" summary="Verify data overrirding on Store View level" ticketId="MAGETWO-50642">
            <data name="originalProduct/dataset" xsi:type="string">bundle_fixed_product</data>
            <data name="store/dataset" xsi:type="string">custom</data>
            <data name="product/data/bundle_selections/dataset" xsi:type="string">fixed_with_custom_title</data>
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductSaveMessage" />
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleOptionTitleOnStorefront" />
        </variation>
    </testCase>
</config>
