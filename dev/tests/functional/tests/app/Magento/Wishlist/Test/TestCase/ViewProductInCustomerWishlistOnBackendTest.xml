<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Wishlist\Test\TestCase\ViewProductInCustomerWishlistOnBackendTest" summary="View Product in Customer Wishlist on Backend" ticketId="MAGETWO-29616">
        <variation name="ViewProductInCustomerWishlistOnBackendTestVariation1">
            <data name="tag" xsi:type="string">to_maintain:yes</data>
            <data name="product/0" xsi:type="string">catalogProductSimple::with_two_custom_option</data>
            <constraint name="Magento\Wishlist\Test\Constraint\AssertProductInCustomerWishlistOnBackendGrid" />
        </variation>
        <variation name="ViewProductInCustomerWishlistOnBackendTestVariation2">
            <data name="product/0" xsi:type="string">configurableProduct::default</data>
            <constraint name="Magento\ConfigurableProduct\Test\Constraint\AssertConfigurableProductInCustomerWishlistOnBackendGrid" />
        </variation>
        <variation name="ViewProductInCustomerWishlistOnBackendTestVariation3">
            <data name="product/0" xsi:type="string">bundleProduct::bundle_dynamic_product</data>
            <constraint name="Magento\Bundle\Test\Constraint\AssertBundleProductInCustomerWishlistOnBackendGrid" />
        </variation>
        <variation name="ViewProductInCustomerWishlistOnBackendTestVariation4">
            <data name="product/0" xsi:type="string">downloadableProduct::with_two_separately_links</data>
            <constraint name="Magento\Downloadable\Test\Constraint\AssertDownloadableProductInCustomerWishlistOnBackendGrid" />
        </variation>
        <variation name="ViewProductInCustomerWishlistOnBackendTestVariation5">
            <data name="product/0" xsi:type="string">groupedProduct::three_simple_products</data>
            <constraint name="Magento\GroupedProduct\Test\Constraint\AssertGroupedProductInCustomerWishlistOnBackendGrid" />
        </variation>
    </testCase>
</config>
