<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Customer\Test\TestCase\UpdateCustomerFrontendEntityTest" summary="Update Customer on Frontend" ticketId="MAGETWO-25925">
        <variation name="UpdateCustomerFrontendEntityTestVariation1" summary="No XSS injection on update customer information add address" ticketId="MAGETWO-47189">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="customer/data/firstname" xsi:type="string">Patrick&lt;/title&gt;&lt;/head&gt;&lt;svg/onload=alert(&#39;XSS&#39;)&gt;</data>
            <data name="customer/data/lastname" xsi:type="string">&lt;script&gt;alert(&#39;Last name&#39;)&lt;/script&gt;</data>
            <data name="customer/data/current_password" xsi:type="string">123123^q</data>
            <data name="address/data/firstname" xsi:type="string">Jany %isolation%</data>
            <data name="address/data/lastname" xsi:type="string">Doe %isolation%</data>
            <data name="address/data/company" xsi:type="string">Company %isolation%</data>
            <data name="address/data/street" xsi:type="string">Some street %isolation%</data>
            <data name="address/data/city" xsi:type="string">City %isolation%</data>
            <data name="address/data/country_id" xsi:type="string">United States</data>
            <data name="address/data/region_id" xsi:type="string">Colorado</data>
            <data name="address/data/telephone" xsi:type="string">***********-999</data>
            <data name="address/data/postcode" xsi:type="string">12345</data>
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerAddressSuccessSaveMessage" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerDefaultAddresses" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerForm" />
        </variation>
        <variation name="UpdateCustomerFrontendEntityTestVariation2" summary="Update customer information and add UK address, assert customer name and address on address book tab">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="customer/data/firstname" xsi:type="string">Jonny %isolation%</data>
            <data name="customer/data/lastname" xsi:type="string">Doe %isolation%</data>
            <data name="customer/data/email" xsi:type="string"><EMAIL></data>
            <data name="customer/data/current_password" xsi:type="string">123123^q</data>
            <data name="address/data/firstname" xsi:type="string">John %isolation%</data>
            <data name="address/data/lastname" xsi:type="string">Doe %isolation%</data>
            <data name="address/data/company" xsi:type="string">Company %isolation%</data>
            <data name="address/data/street" xsi:type="string">Some street %isolation%</data>
            <data name="address/data/city" xsi:type="string">City %isolation%</data>
            <data name="address/data/country_id" xsi:type="string">United Kingdom</data>
            <data name="address/data/region" xsi:type="string">Region %isolation%</data>
            <data name="address/data/telephone" xsi:type="string">0123456789-02134567</data>
            <data name="address/data/postcode" xsi:type="string">12345</data>
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerAddressSuccessSaveMessage" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerDefaultAddresses" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerDefaultAddressFrontendAddressBook" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerNameFrontend" />
        </variation>
        <variation name="UpdateCustomerFrontendEntityTestVariation3" summary="Update customer information and add France address">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="customer/data/firstname" xsi:type="string">Jean %isolation%</data>
            <data name="customer/data/lastname" xsi:type="string">Reno %isolation%</data>
            <data name="customer/data/email" xsi:type="string"><EMAIL></data>
            <data name="customer/data/current_password" xsi:type="string">123123^q</data>
            <data name="address/data/firstname" xsi:type="string">Jean %isolation%</data>
            <data name="address/data/lastname" xsi:type="string">Reno %isolation%</data>
            <data name="address/data/company" xsi:type="string">Magento %isolation%</data>
            <data name="address/data/street" xsi:type="string">18-20 Rue Maréchal Leclerc</data>
            <data name="address/data/city" xsi:type="string">Quintin</data>
            <data name="address/data/country_id" xsi:type="string">France</data>
            <data name="address/data/region_id" xsi:type="string">Côtes-d'Armor</data>
            <data name="address/data/telephone" xsi:type="string">***********-999</data>
            <data name="address/data/postcode" xsi:type="string">12345</data>
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerAddressSuccessSaveMessage" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerDefaultAddresses" />
        </variation>
        <variation name="UpdateCustomerFrontendEntityTestVariation4" summary="Register Customer. Update address" ticketId="MAGETWO-12394">
            <data name="tag" xsi:type="string">test_type:acceptance_test, test_type:extended_acceptance_test</data>
            <data name="customer/data/firstname" xsi:type="string">-</data>
            <data name="customer/data/lastname" xsi:type="string">-</data>
            <data name="customer/data/email" xsi:type="string">-</data>
            <data name="customer/data/current_password" xsi:type="string">123123^q</data>
            <data name="address/data/firstname" xsi:type="string">John</data>
            <data name="address/data/lastname" xsi:type="string">Doe</data>
            <data name="address/data/company" xsi:type="string">Magento %isolation%</data>
            <data name="address/data/street" xsi:type="string">6161 West Centinela Avenue</data>
            <data name="address/data/city" xsi:type="string">Culver City</data>
            <data name="address/data/country_id" xsi:type="string">United States</data>
            <data name="address/data/region_id" xsi:type="string">California</data>
            <data name="address/data/telephone" xsi:type="string">555-55-555-55</data>
            <data name="address/data/postcode" xsi:type="string">90230</data>
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerAddressSuccessSaveMessage" />
            <constraint name="Magento\Customer\Test\Constraint\AssertCustomerDefaultAddresses" />
        </variation>
    </testCase>
</config>
