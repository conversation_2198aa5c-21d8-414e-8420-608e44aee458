<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/etc/pages.xsd">
    <page name="UserRoleEditRole" area="Adminhtml" mca="admin/user_role/editrole" module="Magento_User">
        <block name="pageActions" class="Magento\User\Test\Block\Adminhtml\Role\PageActions" locator=".page-main-actions" strategy="css selector"/>
        <block name="roleFormTabs" class="Magento\User\Test\Block\Adminhtml\Role\RoleForm" locator="[id='page:main-container']" strategy="css selector"/>
        <block name="messagesBlock" class="Magento\Backend\Test\Block\Messages" locator="#messages" strategy="css selector"/>
        <block name="modalBlock" class="Magento\Ui\Test\Block\Adminhtml\Modal" locator="._show[data-role=modal]" strategy="css selector"/>
    </page>
</config>
