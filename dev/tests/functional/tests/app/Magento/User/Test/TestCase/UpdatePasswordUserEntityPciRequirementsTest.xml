<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\User\Test\TestCase\UpdatePasswordUserEntityPciRequirementsTest" summary="PCI password requirements for admin users while changing a passworrd" ticketId="MAGETWO-48104">
        <variation name="UpdatePasswordUserEntityPciRequirementsTestVariation1">
            <data name="user/dataset" xsi:type="string">custom_admin_with_default_role</data>
            <data name="passwords/0" xsi:type="string">123123^q0</data>
            <data name="passwords/1" xsi:type="string">123123^q1</data>
            <data name="passwords/2" xsi:type="string">123123^q2</data>
            <data name="passwords/3" xsi:type="string">123123^q3</data>
            <constraint name="Magento\User\Test\Constraint\AssertUserPasswordChangedSuccessfully" />
        </variation>
    </testCase>
</config>
