<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Backend\Test\TestCase\NavigateMenuTest">
        <variation name="NavigateMenuTest87">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="menuItem" xsi:type="string">Stores > Tax Rules</data>
            <data name="pageTitle" xsi:type="string">Tax Rules</data>
            <constraint name="Magento\Backend\Test\Constraint\AssertBackendPageIsAvailable"/>
        </variation>
        <variation name="NavigateMenuTest88">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="menuItem" xsi:type="string">Stores > Tax Zones and Rates</data>
            <data name="pageTitle" xsi:type="string">Tax Zones and Rates</data>
            <constraint name="Magento\Backend\Test\Constraint\AssertBackendPageIsAvailable"/>
        </variation>
        <variation name="NavigateMenuTest89">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="menuItem" xsi:type="string">System > Import/Export Tax Rates</data>
            <data name="pageTitle" xsi:type="string">Import and Export Tax Rates</data>
            <constraint name="Magento\Backend\Test\Constraint\AssertBackendPageIsAvailable"/>
        </variation>
    </testCase>
</config>
