<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/TestCase/etc/testcase.xsd">
    <scenario name="TaxCalculationTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createSalesRule" />
        <step name="createSalesRule" module="Magento_SalesRule" next="createCatalogRule" />
        <step name="createCatalogRule" module="Magento_CatalogRule" next="createTaxRule" />
        <step name="createTaxRule" module="Magento_Tax" next="createProduct" />
        <step name="createProduct" module="Magento_Catalog" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="loginCustomerOnFrontend" />
        <step name="loginCustomerOnFrontend" module="Magento_Customer" />
    </scenario>
</config>
