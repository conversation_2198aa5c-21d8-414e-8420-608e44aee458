<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Tax\Test\TestCase\CreateTaxRuleEntityTest" summary="Create Tax Rule " ticketId="MAGETWO-20913">
        <variation name="CreateTaxRuleEntityTestVariation1">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">US-CA-Rate_1</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">-</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">-</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">-</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">-</data>
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleInGrid" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleForm" />
        </variation>
        <variation name="CreateTaxRuleEntityTestVariation2">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="tag" xsi:type="string">stable:no</data>
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">US-CA-Rate_1</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">US-NY-Rate_1</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">-</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">product_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">-</data>
            <data name="taxRule/data/priority" xsi:type="string">1</data>
            <data name="taxRule/data/position" xsi:type="string">1</data>
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleInGrid" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleForm" />
        </variation>
        <variation name="CreateTaxRuleEntityTestVariation3">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="description" xsi:type="string">Creating tax rule with new tax classes and tax rate</data>
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">default</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">US-NY-Rate_1</data>
            <data name="taxRule/data/tax_rate/dataset/rate_2" xsi:type="string">US-CA-Rate_1</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">retail_customer</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">taxable_goods</data>
            <data name="taxRule/data/position" xsi:type="string">1</data>
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleInGrid" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleForm" />
        </variation>
        <variation name="CreateTaxRuleEntityTestVariation4">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">withZipRange</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">US-CA-Rate_1</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">retail_customer</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">taxable_goods</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">product_tax_class</data>
            <data name="taxRule/data/priority" xsi:type="string">1</data>
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleInGrid" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleForm" />
        </variation>
        <variation name="CreateTaxRuleEntityTestVariation5" summary="Create Tax Rule with New and Existing Tax Rate, Customer Tax Class, Product Tax Class" ticketId="MAGETWO-12438">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="tag" xsi:type="string">test_type:acceptance_test, test_type:extended_acceptance_test</data>
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">US-CA-*-Rate 1</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">us_ny_rate_8_1</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">retail_customer</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">taxable_goods</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">product_tax_class</data>
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleInGrid" />
            <constraint name="Magento\Tax\Test\Constraint\AssertTaxRuleForm" />
        </variation>
    </testCase>
</config>
