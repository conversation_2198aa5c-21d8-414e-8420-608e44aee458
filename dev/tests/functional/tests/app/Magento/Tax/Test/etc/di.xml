<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Tax\Test\Constraint\AssertTaxRuleInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRuleForm">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateForm">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRuleIsApplied">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxWithCrossBorderApplied">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRuleIsNotApplied">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxWithCrossBorderNotApplied">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRuleSuccessDeleteMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRuleNotInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateSuccessDeleteMessage">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateNotInGrid">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateNotInTaxRule">
        <arguments>
            <argument name="severity" xsi:type="string">high</argument>
        </arguments>
    </type>
    <type name="Magento\Tax\Test\Constraint\AssertTaxRateInTaxRule">
        <arguments>
            <argument name="severity" xsi:type="string">middle</argument>
        </arguments>
    </type>
</config>
