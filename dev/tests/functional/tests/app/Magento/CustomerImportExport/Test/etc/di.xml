<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <virtualType name="Magento\Mtf\Util\Command\File\CustomerAddressesExport" type="Magento\Mtf\Util\Command\File\Export">
        <arguments>
            <argument name="type" xsi:type="string">customerAddresses</argument>
        </arguments>
    </virtualType>

    <type name="Magento\CustomerImportExport\Test\Constraint\AssertExportCustomerAddresses">
        <arguments>
            <argument name="export" xsi:type="object">Magento\Mtf\Util\Command\File\CustomerAddressesExport</argument>
        </arguments>
    </type>

    <virtualType name="Magento\Mtf\Util\Command\File\Export\CustomerAddressesReader" type="Magento\Mtf\Util\Command\File\Export\Reader">
        <arguments>
            <argument name="template" xsi:type="string">customer_address.*?\.csv</argument>
        </arguments>
    </virtualType>
</config>
