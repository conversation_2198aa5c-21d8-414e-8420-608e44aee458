<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/etc/pages.xsd">
    <page name="SalesOrderView" area="Adminhtml" mca="sales/order/view" module="Magento_Sales">
        <block name="pageActions" class="Magento\Sales\Test\Block\Adminhtml\Order\Actions" locator=".page-actions:not([data-mage-init])" strategy="css selector" />
        <block name="messagesBlock" class="Magento\Backend\Test\Block\Messages" locator="#messages" strategy="css selector" />
        <block name="orderForm" class="Magento\Sales\Test\Block\Adminhtml\Order\View\OrderForm" locator="[id='page:main-container']" strategy="css selector" />
        <block name="titleBlock" class="Magento\Theme\Test\Block\Html\Title" locator=".page-title-wrapper" strategy="css selector" />
        <block name="itemsOrderedBlock" class="Magento\Sales\Test\Block\Adminhtml\Order\View\Items" locator="#sales_order_view_tabs_order_info_content .edit-order-table" strategy="css selector" />
        <block name="orderTotalsBlock" class="Magento\Sales\Test\Block\Adminhtml\Order\Totals" locator=".order-totals" strategy="css selector" />
        <block name="informationBlock" class="Magento\Sales\Test\Block\Adminhtml\Order\View\Info" locator=".order-account-information" strategy="css selector" />
        <block name="orderInfoBlock" class="Magento\Sales\Test\Block\Adminhtml\Order\View\Tab\Info" locator="[data-ui-id='sales-order-tabs-tab-content-order-info']" strategy="css selector" />
        <block name="orderInvoiceGrid" class="Magento\Sales\Test\Block\Adminhtml\Invoice\Grid" locator="//div[contains(@data-bind, 'sales_order_view_invoice_grid.sales_order_view_invoice_grid')]" strategy="xpath" />
        <block name="addressesBlock" class="Magento\Sales\Test\Block\Adminhtml\Order\View\Addresses" locator=".admin__page-section.order-addresses" strategy="css selector" />
    </page>
</config>
