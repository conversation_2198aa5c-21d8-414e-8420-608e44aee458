<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Sales\Test\TestCase\CreateOrderBackendTest" summary="Create Order from Admin within Offline Payment Methods" ticketId="MAGETWO-28696">
        <variation name="CreateOrderBackendTestVariation18" summary="Create order with condition available product qty = ordered product qty" ticketId="MAGETWO-12798">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="products/0" xsi:type="string">catalogProductSimple::product_with_qty_25</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="billingAddress/dataset" xsi:type="string">US_address_1_without_email</data>
            <data name="saveAddress" xsi:type="string">No</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="prices" xsi:type="array">
                <item name="grandTotal" xsi:type="string">375.00</item>
            </data>
            <data name="payment/method" xsi:type="string">cashondelivery</data>
            <data name="configData" xsi:type="string">cashondelivery</data>
            <constraint name="Magento\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Magento\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Magento\Catalog\Test\Constraint\AssertProductsOutOfStock" />
        </variation>
        <variation name="CreateOrderBackendTestVariation19" summary="'Reorder' button is not visible for customer if ordered item is out of stock" ticketId="MAGETWO-63924">
            <data name="tag" xsi:type="string">mftf_migrated:yes</data>
            <data name="products/0" xsi:type="string">catalogProductSimple::default_qty_1</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="billingAddress/dataset" xsi:type="string">US_address_1_without_email</data>
            <data name="saveAddress" xsi:type="string">No</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="prices" xsi:type="array">
                <item name="grandTotal" xsi:type="string">565.00</item>
            </data>
            <data name="payment/method" xsi:type="string">cashondelivery</data>
            <data name="configData" xsi:type="string">cashondelivery</data>
            <constraint name="Magento\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Magento\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Magento\Sales\Test\Constraint\AssertReorderButtonIsNotVisibleOnFrontend" />
        </variation>
    </testCase>
</config>
