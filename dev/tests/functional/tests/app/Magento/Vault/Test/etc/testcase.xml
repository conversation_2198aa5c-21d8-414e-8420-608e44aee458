<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/TestCase/etc/testcase.xsd">
    <scenario name="UseVaultOnCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="estimateShippingAndTax" />
        <step name="estimateShippingAndTax" module="Magento_Checkout" next="clickProceedToCheckout" />
        <step name="clickProceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="saveCreditCard" />
        <step name="saveCreditCard" module="Magento_Vault" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="placeOrder" />
        <step name="placeOrder" module="Magento_Checkout" next="addProductsToTheCartVault" />
        <step name="addProductsToTheCartVault" alias="addProductsToTheCart" module="Magento_Checkout" next="estimateShippingAndTaxVault" />
        <step name="estimateShippingAndTaxVault" alias="estimateShippingAndTax" module="Magento_Checkout" next="clickProceedToCheckoutVault" />
        <step name="clickProceedToCheckoutVault" alias="clickProceedToCheckout" module="Magento_Checkout" next="fillShippingMethodVault" />
        <step name="fillShippingMethodVault" alias="fillShippingMethod" module="Magento_Checkout" next="useSavedPaymentMethod" />
        <step name="useSavedPaymentMethod" module="Magento_Vault" next="placeOrderVault" />
        <step name="placeOrderVault" alias="placeOrder" module="Magento_Checkout" />
    </scenario>
    <scenario name="CreateVaultOrderBackendTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="openSalesOrders" />
        <step name="openSalesOrders" module="Magento_Sales" next="createNewOrder" />
        <step name="createNewOrder" module="Magento_Sales" next="selectCustomerOrder" />
        <step name="selectCustomerOrder" module="Magento_Sales" next="selectStore" />
        <step name="selectStore" module="Magento_Sales" next="addProducts" />
        <step name="addProducts" module="Magento_Sales" next="fillAccountInformation" />
        <step name="fillAccountInformation" module="Magento_Sales" next="fillBillingAddress" />
        <step name="fillBillingAddress" module="Magento_Sales" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Sales" next="selectShippingMethodForOrder" />
        <step name="selectShippingMethodForOrder" module="Magento_Sales" next="saveCreditCardOnBackend" />
        <step name="saveCreditCardOnBackend" module="Magento_Vault" next="submitOrder" />
        <step name="submitOrder" module="Magento_Sales" next="reorder" />
        <step name="reorder" module="Magento_Sales" next="useVaultPaymentToken" />
        <step name="useVaultPaymentToken" module="Magento_Vault" next="submitOrderWithVault" />
        <step name="submitOrderWithVault" module="Magento_Vault" />
    </scenario>
    <scenario name="ReorderUsingVaultTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Magento_Config" next="createProducts" />
        <step name="createProducts" module="Magento_Catalog" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Magento_Checkout" next="estimateShippingAndTax" />
        <step name="estimateShippingAndTax" module="Magento_Checkout" next="clickProceedToCheckout" />
        <step name="clickProceedToCheckout" module="Magento_Checkout" next="createCustomer" />
        <step name="createCustomer" module="Magento_Customer" next="selectCheckoutMethod" />
        <step name="selectCheckoutMethod" module="Magento_Checkout" next="fillShippingAddress" />
        <step name="fillShippingAddress" module="Magento_Checkout" next="fillShippingMethod" />
        <step name="fillShippingMethod" module="Magento_Checkout" next="selectPaymentMethod" />
        <step name="selectPaymentMethod" module="Magento_Checkout" next="checkSaveCreditCardOption" />
        <step name="checkSaveCreditCardOption" module="Magento_Vault" next="fillBillingInformation" />
        <step name="fillBillingInformation" module="Magento_Checkout" next="placeOrder" />
        <step name="placeOrder" module="Magento_Checkout" next="openOrder" />
        <step name="openOrder" module="Magento_Sales" next="reorder" />
        <step name="reorder" module="Magento_Sales" next="useVaultPaymentToken" />
        <step name="useVaultPaymentToken" module="Magento_Vault" next="submitOrder" />
        <step name="submitOrder" module="Magento_Sales" />
    </scenario>
    <scenario name="OnePageCheckoutTest">
        <step name="checkSaveCreditCardOption" module="Magento_Vault" prev="selectPaymentMethod" next="placeOrder" />
    </scenario>
</config>
