<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Magento\Reports\Test\TestCase\SalesRefundsReportEntityTest" summary="Sales Refunds Report" ticketId="MAGETWO-29348">
        <variation name="SalesRefundsReportEntityTestVariation1">
            <data name="description" xsi:type="string">assert refunds year report</data>
            <data name="order/dataset" xsi:type="string">default</data>
            <data name="order/data/price/dataset" xsi:type="string">full_invoice</data>
            <data name="refundsReport/report_type" xsi:type="string">Order Created</data>
            <data name="refundsReport/period_type" xsi:type="string">Year</data>
            <data name="refundsReport/from" xsi:type="string">m/d/Y 12:00 a-2 days</data>
            <data name="refundsReport/to" xsi:type="string">m/d/Y 12:00 a+2 days</data>
            <data name="refundsReport/show_order_statuses" xsi:type="string">Any</data>
            <data name="refundsReport/show_empty_rows" xsi:type="string">Yes</data>
            <constraint name="Magento\Reports\Test\Constraint\AssertRefundReportIntervalResult" />
        </variation>
        <variation name="SalesRefundsReportEntityTestVariation2">
            <data name="description" xsi:type="string">assert refunds month report</data>
            <data name="order/dataset" xsi:type="string">default</data>
            <data name="order/data/price/dataset" xsi:type="string">full_invoice</data>
            <data name="refundsReport/report_type" xsi:type="string">Order Created</data>
            <data name="refundsReport/period_type" xsi:type="string">Month</data>
            <data name="refundsReport/from" xsi:type="string">m/d/Y</data>
            <data name="refundsReport/to" xsi:type="string">m/d/Y</data>
            <data name="refundsReport/show_order_statuses" xsi:type="string">Any</data>
            <data name="refundsReport/show_empty_rows" xsi:type="string">No</data>
            <constraint name="Magento\Reports\Test\Constraint\AssertRefundReportIntervalResult" />
        </variation>
        <variation name="SalesRefundsReportEntityTestVariation3">
            <data name="description" xsi:type="string">assert refund Daily report</data>
            <data name="order/dataset" xsi:type="string">default</data>
            <data name="order/data/price/dataset" xsi:type="string">full_invoice</data>
            <data name="refundsReport/report_type" xsi:type="string">Last Credit Memo Created Date</data>
            <data name="refundsReport/period_type" xsi:type="string">Day</data>
            <data name="refundsReport/from" xsi:type="string">m/d/Y</data>
            <data name="refundsReport/to" xsi:type="string">m/d/Y</data>
            <data name="refundsReport/show_order_statuses" xsi:type="string">Specified</data>
            <data name="refundsReport/show_empty_rows" xsi:type="string">No</data>
            <constraint name="Magento\Reports\Test\Constraint\AssertRefundReportIntervalResult" />
        </variation>
    </testCase>
</config>
