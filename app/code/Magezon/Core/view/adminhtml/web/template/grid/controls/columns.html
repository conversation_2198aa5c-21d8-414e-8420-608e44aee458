<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="admin__action-dropdown-wrap admin__data-grid-action-columns" collapsible>
    <button class="admin__action-dropdown" type="button" toggleCollapsible>
        <span class="admin__action-dropdown-text" translate="'Columns'"></span>
    </button>
    <div class="admin__action-dropdown-menu admin__data-grid-action-columns-menu" css="_overflow: hasOverflow()">
        <div class="admin__action-dropdown-menu-header" text="getHeaderMessage()"></div>
        <div class="admin__action-dropdown-menu-content">
            <div repeat="foreach: elems, item: '$col'" attr="class: 'admin__field-option ' + 'col_' + $col().index">
                <input class="admin__control-checkbox" type="checkbox"
                    disable="isDisabled($col())"
                    ko-checked="$col().visible"
                    attr="id: ++ko.uid"/>
                <label class="admin__field-label"
                       translate="$col().label"
                       attr="for: ko.uid, title: $col().label"></label>
            </div>
        </div>
        <div class="admin__action-dropdown-menu-footer">
            <div class="admin__action-dropdown-footer-secondary-actions">
                <button class="action-tertiary" type="button" click="reset" translate="'Reset'"></button>
            </div>
            <div class="admin__action-dropdown-footer-main-actions">
                <button class="action-tertiary" type="button" click="cancel" translate="'Cancel'" closeCollapsible></button>
            </div>
        </div>
    </div>
</div>
