<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_Builder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../Config/etc/system_file.xsd">
	<system>
		<section id="mgzbuilder" translate="label" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
			<class>separator-top</class>
			<label>Magezon Builder</label>
			<tab>magezon</tab>
			<resource>Magezon_Builder::settings</resource>
			<group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>General Settings</label>
				<field id="row_inner_width" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Row Inner Width</label>
				</field>
				<field id="google_api_key" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Google Map API key</label>
					<comment><![CDATA[Input Google API key]]></comment>
				</field>
			</group>
			<group id="customization" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Customization Settings</label>
				<field id="css" translate="label" type="textarea" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Custom CSS</label>
				</field>
			</group>
		</section>
	</system>
</config>