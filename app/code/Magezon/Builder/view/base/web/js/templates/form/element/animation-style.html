<select name="{{ options.key }}" class="mgz__control-select" ng-options="option[to.valueProp || 'value'] as option[to.labelProp || 'label'] group by option[to.groupProp || 'group'] for option in styles" ng-model="model[options.key]" ng-change="loadAnimate()"><option ng-if="to.placeholder != ''" value="">{{to.placeholder}}</option></select>
<div ng-class="animateClass" style=" display: inline-block; ">
	<button type="button" class="mgz-btn" ng-click="loadAnimate()">Animate it</button>
</div>