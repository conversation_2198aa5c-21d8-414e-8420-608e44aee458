<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_NinjaMenus
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::content">
                    <resource id="Magento_Backend::content_elements">
                        <resource id="Magezon_NinjaMenus::ninjamenus" title="Ninja Menus" sortOrder="10">
                            <resource id="Magezon_NinjaMenus::menu" title="Menu" translate="title" sortOrder="10">
                                <resource id="Magezon_NinjaMenus::menu_save" title="Save Menu" sortOrder="10" />
                                <resource id="Magezon_NinjaMenus::menu_delete" title="Delete Menu" sortOrder="20" />
                            </resource>
                        </resource>
                    </resource>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Magezon_NinjaMenus::settings" title="Ninja Menus" translate="title" />
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>