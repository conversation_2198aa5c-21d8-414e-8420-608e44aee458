<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Customer\Controller\Account\CreatePost" type="Fhr\Customer\Rewrite\Magento\Customer\Controller\Account\CreatePost"/>
    <preference for="Magento\Customer\Controller\Account\Confirm" type="Fhr\Customer\Rewrite\Magento\Customer\Controller\Account\Confirm"/>
    <type name="Magento\Customer\Api\AccountManagementInterface">
        <plugin name="check_customer_group" type="Fhr\Customer\Plugin\AccountManagement" />
    </type>
    <type name="Magento\Customer\Model\Session">
        <plugin disabled="false"
                sortOrder="1"
                name="Fhr_Customer_Plugin_Frontend_Magento_Customer_Model_Session"
                type="Fhr\Customer\Plugin\Frontend\Magento\Customer\Model\Session"/>
    </type>
</config>