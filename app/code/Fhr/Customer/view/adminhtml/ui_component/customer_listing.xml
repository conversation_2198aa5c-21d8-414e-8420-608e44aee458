<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <columns name="customer_columns">
        <column name="group_id">
            <settings>
                <editor>
                    <editorType>none</editorType>
                </editor>
            </settings>
        </column>
        <column name="billing_company">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>
