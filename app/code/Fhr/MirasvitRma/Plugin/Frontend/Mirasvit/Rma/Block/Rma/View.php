<?php

declare(strict_types=1);

namespace Fhr\MirasvitRma\Plugin\Frontend\Mirasvit\Rma\Block\Rma;

class View
{

    /**
     * @param \Mirasvit\Rma\Block\Rma\View $subject
     * @param array $progress
     * @return array
     */
    public function afterGetProgress(
        \Mirasvit\Rma\Block\Rma\View $subject,
        $progress
    ) {
        if (isset($progress[7]) && $progress[7]['active']) {
            $progress[6]['active'] = false;
        }
        return $progress;
    }
}
