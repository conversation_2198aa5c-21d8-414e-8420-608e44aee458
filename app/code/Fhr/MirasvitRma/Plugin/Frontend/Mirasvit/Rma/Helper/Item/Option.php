<?php

declare(strict_types=1);

namespace Fhr\MirasvitRma\Plugin\Frontend\Mirasvit\Rma\Helper\Item;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Config\ScopeConfigInterface;

class Option
{
    const XML_PATH_DISABLE_REFUND_CUSTOMER_GROUP = 'fhrmirasvitrma/general/disable_refund_customer_group';
    const XML_PATH_ADDTAXAMOUNT = 'fhrmirasvitrma/general/addtaxamount';
    const RMA_RESOLUTIONS_REFUND_ID = 2;

    /**
     * @var Session
     */
    private $customerSession;
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * Option constructor.
     * @param CustomerSession $customerSession
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        CustomerSession $customerSession,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->customerSession = $customerSession;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param \Mirasvit\Rma\Helper\Item\Option $subject
     * @param \Mirasvit\Rma\Api\Data\ResolutionInterface[] $result
     * @return array|mixed
     */
    public function afterGetResolutionList(
        \Mirasvit\Rma\Helper\Item\Option $subject,
        array $result
    ) {
        if (is_array($result)) {
            foreach ($result as $key => $value) {
                if ($value->getResolutionId() == self::RMA_RESOLUTIONS_REFUND_ID) {
                    if ($this->disableRefund()) {
                        unset($result[$key]);
                    }
                }
            }
        }
        return $result;
    }

    /**
     * @return bool
     */
    public function disableRefund()
    {
        $disabledCustomersGroups = explode(
            ',',
            $this->scopeConfig->getValue(self::XML_PATH_DISABLE_REFUND_CUSTOMER_GROUP)
        ) ;
        if ($this->customerSession->isLoggedIn()) {
            return in_array($this->customerSession->getCustomer()->getGroupId(), $disabledCustomersGroups);
        }
        return false;
    }

    /**
     * @return bool
     */
    public function isAddTax()
    {
        return (boolean)$this->scopeConfig->getValue(self::XML_PATH_ADDTAXAMOUNT);
    }
}
