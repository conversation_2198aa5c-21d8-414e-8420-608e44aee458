<?php


namespace Fhr\MirasvitRma\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface RmaRepositoryInterface
{
    /**
     * Retrieve page.
     *
     * @param int $rmaId
     * @return \Fhr\MirasvitRma\Api\Data\RmaInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($rmaId);

    /**
     * Retrieve pages matching the specified criteria.
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Fhr\MirasvitRma\Api\Data\RmaSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(\Magento\Framework\Api\SearchCriteriaInterface $searchCriteria);
}
