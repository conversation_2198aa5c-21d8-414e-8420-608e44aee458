<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="fhr" translate="label" sortOrder="-100">
            <label>Fhr Configs</label>
        </tab>
        <section id="fhrbase" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="0" translate="label">
            <label>Fhr Base</label>
            <tab>fhr</tab>
            <resource>Fhr_Base::config_fhr_base</resource>
            <group id="general" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
                <label>General Options</label>
                <field id="fromemail" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Defaul from email</label>
                </field>
                <field id="fromname" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Defaul from name</label>
                </field>
            </group>
            <group id="frontcontrols" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
                <label>Front controls</label>
                <field id="displayrealstockqty" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display real stock qty</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>