<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\XmlExportCustomer\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface ProductsRepositoryInterface
{

    /**
     * Save Products
     * @param \Fhr\XmlExportCustomer\Api\Data\ProductsInterface $products
     * @return \Fhr\XmlExportCustomer\Api\Data\ProductsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Fhr\XmlExportCustomer\Api\Data\ProductsInterface $products
    );

    /**
     * Retrieve Products
     * @param string $id
     * @return \Fhr\XmlExportCustomer\Api\Data\ProductsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($id);

    /**
     * Retrieve Products matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Fhr\XmlExportCustomer\Api\Data\ProductsSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Products
     * @param \Fhr\XmlExportCustomer\Api\Data\ProductsInterface $products
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Fhr\XmlExportCustomer\Api\Data\ProductsInterface $products
    );

    /**
     * Delete Products by ID
     * @param string $id
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($id);
}
