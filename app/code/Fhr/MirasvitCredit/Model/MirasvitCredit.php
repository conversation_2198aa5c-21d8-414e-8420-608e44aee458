<?php


namespace Fhr\MirasvitCredit\Model;

use Fhr\Base\Api\CommonInterface;
use Fhr\Base\Model\Mail\SendEmail;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Fhr\MirasvitCredit\Api\ApprovedTransactionsRepositoryInterface;
use Magento\Backend\Model\Auth\Session as AuthSession;
use Magento\Framework\Url;
use Magento\Framework\Encryption\EncryptorInterface;
use Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;

class MirasvitCredit
{
    const XML_PATH_ENABLED = 'fhrmirasvitcredit/general/enabled';
    const XML_PATH_EMAILS  = 'fhrmirasvitcredit/general/emails';
    const XML_PATH_TEMPLATE  = 'fhrmirasvitcredit/general/template';
    const KEY = 'ab7b281ae14f43b2d8bd853a70c6aceab6e4a3b4028cd9922e793d5a781d1e70';

    /**
     * @var SendEmail
     */
    private $sendEmail;
    /**
     * @var CommonInterface
     */
    private $common;
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;
    /**
     * @var ApprovedTransactionsRepositoryInterface
     */
    private $approvedTransactionsRepository;
    /**
     * @var AuthSession
     */
    private $authSession;
    /**
     * @var Url
     */
    private $url;
    /**
     * @var EncryptorInterface
     */
    private $encryptor;
    /**
     * @var ResourceConnection
     */
    private $connection;

    /**
     * MirasvitCredit constructor.
     * @param CommonInterface $common
     * @param CustomerRepositoryInterface $customerRepository
     * @param SendEmail $sendEmail
     * @param ApprovedTransactionsRepositoryInterface $approvedTransactionsRepository
     * @param AuthSession $authSession
     * @param Url $url
     * @param EncryptorInterface $encryptor
     * @param AdapterInterface $connection
     */
    public function __construct(
        CommonInterface $common,
        CustomerRepositoryInterface $customerRepository,
        SendEmail $sendEmail,
        ApprovedTransactionsRepositoryInterface $approvedTransactionsRepository,
        AuthSession $authSession,
        Url $url,
        EncryptorInterface $encryptor,
        ResourceConnection $resource
    ) {
        $this->sendEmail = $sendEmail;
        $this->common = $common;
        $this->customerRepository = $customerRepository;
        $this->approvedTransactionsRepository = $approvedTransactionsRepository;
        $this->authSession = $authSession;
        $this->url = $url;
        $this->encryptor = $encryptor;
        $this->connection = $resource->getConnection();
    }

    /**
     * @param \Magento\User\Model\User $user
     * @param array $data <p>Request params data</p>
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function sendMail($user, $data)
    {
        $templateId = $this->common->getConfigValue(self::XML_PATH_TEMPLATE);
        $toEmails = $this->common->configEmailsToArray(
            $this->common->getConfigValue(self::XML_PATH_EMAILS)
        );
        $date = date('YmdHis');
        foreach ($toEmails as $to) {
            $subject = __('Customer credit request from user %1', $user->getUserName());
            $message = $subject.".\n ".__(
                'Press link %1 to view customer credit request. '
                .'Press "Save" button on "Transaction" page to approve. Details: %2',
                $this->getLinkToApprove(
                    $data['customer_id'],
                    $data['balance_delta'],
                    $data['currency_code'],
                    $data['message'],
                    $date,
                    $to['email']
                ),
                $this->getDetails($data)
            );
            $templateVars = [
                'subject' => $subject,
                'customer_name' => $to['name'],
                'message' => $message
            ];
            $this->sendEmail->sendEmail($templateId, $to['email'], $to['name'], $templateVars);
        }
    }

    /**
     * Check if module enabled
     * @return bool
     */
    public function isEnabled()
    {
        return (bool) $this->common->getConfigValue(self::XML_PATH_ENABLED);
    }

    public function getLinkToApprove($customerId, $balanceDelta, $currencyCode, $message, $date, $email)
    {
        $frontUrl = $this->url->getUrl(
            'fhrcredit/approvedtransaction',
            [
                'customer_id' => $customerId,
                'balance_delta' => $balanceDelta,
                'currency_code' => $currencyCode,
                'message' => base64_encode($message),
                'emailkey' => md5($customerId.$balanceDelta.$currencyCode.$date),
                'approver' => $this->approverForLink($email)
            ]
        );

        return '<a href="'.$frontUrl.'" target="_blanc">'.__('Approve').'</a>';
    }

    public function approverForLink($email)
    {
        $row = $this->getUserByEmail($email);
        return base64_encode(
            $this->encryptor->encrypt(
                $row['user_id'].';'.
                $row['firstname'].';'.
                $row['lastname'].';'.
                $row['email'].';'.
                $row['username']
            )
        );
    }

    /**
     * @param string $email
     * @return array
     */
    public function getUserByEmail($email)
    {
        $table = $this->connection->getTableName('admin_user');
        $select = $this->connection->select()
            ->from($table)
            ->where('email = ?', $email)
            ->limit(1);
        return $this->connection->fetchRow($select);
    }

    /**
     * @param $data
     * @return \Magento\Framework\Phrase
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getDetails($data)
    {
        $customer = $this->customerRepository->getById($data['customer_id']);
        return __(
            'Customer ID: %1, Name: %2, Email: %3, Credit Amount: %4. %5',
            $customer->getId(),
            $customer->getFirstname().' '.$customer->getLastname(),
            $customer->getEmail(),
            $data['balance_delta'].' '.$data['currency_code'],
            $data['message']
        );
    }

    public function getApprovers()
    {
        return $this->common->getConfigValue(self::XML_PATH_EMAILS);
    }

    /**
     * @param string $emailkey
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function checkForDuplicate($emailkey)
    {
        return $this->approvedTransactionsRepository->getByEmailkey($emailkey);
    }

    /**
     * @param $emailkey
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function saveApprovedTransaction($params)
    {
        $approvedTransaction = $this->approvedTransactionsRepository->getByEmailkey(
            $params[ApprovedTransactionsInterface::EMAILKEY]
        );
        $approvedTransaction->setEmailkey($params[ApprovedTransactionsInterface::EMAILKEY]);
        $approvedTransaction->setEmail($params[ApprovedTransactionsInterface::EMAIL]);
        $approvedTransaction->setUserId($params[ApprovedTransactionsInterface::USER_ID]);
        $approvedTransaction->setFirstname($params[ApprovedTransactionsInterface::FIRSTNAME]);
        $approvedTransaction->setLastname($params[ApprovedTransactionsInterface::LASTNAME]);
        $approvedTransaction->setUsername($params[ApprovedTransactionsInterface::USERNAME]);
        return $this->approvedTransactionsRepository->save($approvedTransaction);
    }
}
