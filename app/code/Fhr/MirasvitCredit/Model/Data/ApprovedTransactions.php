<?php
declare(strict_types=1);

namespace Fhr\MirasvitCredit\Model\Data;

use Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface;
use Fhr\MirasvitCredit\Api\Data\FhrMirasvitCreditApiDataApprovedTransactionsInterface;

class ApprovedTransactions extends \Magento\Framework\Api\AbstractExtensibleObject implements ApprovedTransactionsInterface
{

    /**
     * Get approvedtransactions_id
     * @return string|null
     */
    public function getApprovedtransactionsId()
    {
        return $this->_get(self::APPROVEDTRANSACTIONS_ID);
    }

    /**
     * Set approvedtransactions_id
     * @param string $approvedtransactionsId
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setApprovedtransactionsId($approvedtransactionsId)
    {
        return $this->setData(self::APPROVEDTRANSACTIONS_ID, $approvedtransactionsId);
    }

    /**
     * Get transaction_id
     * @return string|null
     */
    public function getTransactionId()
    {
        return $this->_get(self::TRANSACTION_ID);
    }

    /**
     * Set transaction_id
     * @param string $transactionId
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setTransactionId($transactionId)
    {
        return $this->setData(self::TRANSACTION_ID, $transactionId);
    }

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set an extension attributes object.
     * @param \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsExtensionInterface $extensionAttributes
    ) {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @return mixed|string|null
     */
    public function getCreatedAt()
    {
        return $this->_get(self::CREATED_AT);
    }

    /**
     * @param string $createdAt
     * @return ApprovedTransactionsInterface|ApprovedTransactions
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @return mixed|string|null
     */
    public function getUpdatedAt()
    {
        return $this->_get(self::UPDATED_AT);
    }

    /**
     * @param string $updatedAt
     * @return ApprovedTransactionsInterface|ApprovedTransactions
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @return mixed|string|null
     */
    public function getParams()
    {
        return $this->_get(self::PARAMS);
    }

    /**
     * @param string $params
     * @return ApprovedTransactionsInterface|ApprovedTransactions
     */
    public function setParams($params)
    {
        return $this->setData(self::PARAMS, $params);
    }

    public function getUserId()
    {
        return $this->_get(self::USER_ID);
    }

    public function setUserId($user_id)
    {
        return $this->setData(self::USER_ID, $user_id);
    }

    public function getEmail()
    {
        return $this->_get(self::EMAIL);
    }

    public function setEmail($email)
    {
        return $this->setData(self::EMAIL, $email);
    }

    public function getEmailkey()
    {
        return $this->_get(self::EMAILKEY);
    }

    public function setEmailkey($emailkey)
    {
        return $this->setData(self::EMAILKEY, $emailkey);
    }

    public function getFirstname()
    {
        return $this->_get(self::FIRSTNAME);
    }

    public function setFirstname($firstname)
    {
        return $this->setData(self::FIRSTNAME, $firstname);
    }

    public function getLastname()
    {
        return $this->_get(self::LASTNAME);
    }

    public function setLastname($lastname)
    {
        return $this->setData(self::LASTNAME, $lastname);
    }

    public function getUsername()
    {
        return $this->_get(self::USERNAME);
    }

    public function setUsername($username)
    {
        return $this->setData(self::USERNAME, $username);
    }
}
