<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="fhrmirasvitcredit" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="140"
                 translate="label">
            <label>Fhr Mirasvit Credit</label>
            <tab>fhr</tab>
            <resource>Fhr_MirasvitCredit::config_mirasvitcredit</resource>
            <group id="general" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="10" translate="label">
                <label>General Options</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="emails" translate="label" type="textarea" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Email List</label>
                    <comment><![CDATA[Please enter each email in new line,
                    Separate e-mail and name with semicolon.
                    Example <EMAIL>;Linus Hilmgård]]></comment>
                </field>
                <field id="template" translate="label comment" type="select" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Email Template</label>
                    <comment>Email template.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
