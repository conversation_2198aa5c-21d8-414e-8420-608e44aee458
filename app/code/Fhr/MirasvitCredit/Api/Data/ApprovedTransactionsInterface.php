<?php
declare(strict_types=1);

namespace Fhr\MirasvitCredit\Api\Data;

interface ApprovedTransactionsInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{

    const TRANSACTION_ID = 'transaction_id';
    const APPROVEDTRANSACTIONS_ID = 'approvedtransactions_id';
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const PARAMS = 'params';
    const USER_ID = 'user_id';
    const EMAIL = 'email';
    const EMAILKEY = 'emailkey';
    const FIRSTNAME = 'firstname';
    const LASTNAME = 'lastname';
    const USERNAME = 'username';

    /**
     * Get approvedtransactions_id
     * @return string|null
     */
    public function getApprovedtransactionsId();

    /**
     * Set approvedtransactions_id
     * @param string $approvedtransactionsId
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setApprovedtransactionsId($approvedtransactionsId);

    /**
     * Get transaction_id
     * @return string|null
     */
    public function getTransactionId();

    /**
     * Set transaction_id
     * @param string $transactionId
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setTransactionId($transactionId);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setCreatedAt($createdAt);
    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt();

    /**
     * Set updated_at
     * @param string $updatedAt
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setUpdatedAt($updatedAt);
    /**
     * Get params
     * @return string|null
     */
    public function getParams();

    /**
     * Set params
     * @param string $params
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setParams($params);

    /**
     * Get user_id
     * @return string|null
     */
    public function getUserId();

    /**
     * Set user_id
     * @param string $user_id
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setUserId($user_id);
    /**
     * Get email
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     * @param string $email
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setEmail($email);
    /**
     * Get emailkey
     * @return string|null
     */
    public function getEmailkey();

    /**
     * Set emailkey
     * @param string $emailkey
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsInterface
     */
    public function setEmailkey($emailkey);

    /**
     * Get firstname
     * @return string|null
     */
    public function getFirstname();

    /**
     * Set firstname
     * @param string $firstname
     * @return FhrMirasvitCreditApiDataApprovedTransactionsInterface
     */
    public function setFirstname($firstname);
    /**
     * Get lastname
     * @return string|null
     */
    public function getLastname();

    /**
     * Set lastname
     * @param string $lastname
     * @return FhrMirasvitCreditApiDataApprovedTransactionsInterface
     */
    public function setLastname($lastname);
    /**
     * Get username
     * @return string|null
     */
    public function getUsername();

    /**
     * Set username
     * @param string $username
     * @return FhrMirasvitCreditApiDataApprovedTransactionsInterface
     */
    public function setUsername($username);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Fhr\MirasvitCredit\Api\Data\ApprovedTransactionsExtensionInterface $extensionAttributes
    );
}
