<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\PromoGroups\Api\Data;

interface PromoGroupsSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get PromoGroups list.
     * @return \Fhr\PromoGroups\Api\Data\PromoGroupsInterface[]
     */
    public function getItems();

    /**
     * Set Promogroup list.
     * @param \Fhr\PromoGroups\Api\Data\PromoGroupsInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
