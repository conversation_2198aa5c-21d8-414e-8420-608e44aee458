<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="extrashippingcost" translate="label" type="text" sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Extra Shipping Cost</label>
            <tab>fhr</tab>
            <resource>Fhr_Extrashippingcost::config_extrashippingcost</resource>
            <group id="settings" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Settings</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="max_extra_cost" translate="label" type="text" sortOrder="20" showInDefault="0" showInWebsite="1" showInStore="0">
                    <label>Maximum Extra Cost</label>
                    <validate>validate-number validate-zero-or-greater</validate>
                </field>
                <field id="additional_cost" translate="label" type="text" sortOrder="30" showInDefault="0" showInWebsite="1" showInStore="0">
                    <label>Additional Cost per Unit</label>
                    <validate>validate-number validate-zero-or-greater</validate>
                </field>
                <field id="used_phones_category" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Category ID's</label>
                    <comment>Enter the category ID to apply extra shipping cost</comment>
                </field>
                <field id="disallowed_countries" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Disallowed Countries</label>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                    <can_be_empty>1</can_be_empty>
                </field>
            </group>
        </section>
    </system>
</config>
