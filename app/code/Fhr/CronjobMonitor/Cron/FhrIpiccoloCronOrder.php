<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\CronjobMonitor\Cron;

class FhrIpiccoloCronOrder
{

    protected $logger;
    /**
     * @var \Fhr\CronjobMonitor\Model\Common
     */
    private $common;

    /**
     * Constructor
     *
     * @param \Fhr\CronjobMonitor\Model\Common $common
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Fhr\CronjobMonitor\Model\Common $common,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->common = $common;
        $this->logger = $logger;
    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute()
    {
        $this->common->sendEmailFhrIpiccoloCronOrder();
        $this->logger->addInfo("Cronjob FhrIpiccoloCronOrder is executed.");
    }
}
