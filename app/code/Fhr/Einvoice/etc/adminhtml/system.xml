<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<section id="payment" sortOrder="1000" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
			<group id="einvoice" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
				<label>EInvoice</label>
				<field id="active" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" type="text" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Title</label>
				</field>
				<field id="order_status" type="select" sortOrder="30" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>New Order Status</label>
					<source_model>Magento\Sales\Model\Config\Source\Order\Status\NewStatus</source_model>
				</field>
				<field id="allowspecific" type="allowspecific" sortOrder="40" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Payment from Applicable Countries</label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" type="multiselect" sortOrder="50" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Payment from Applicable Countries</label>
					<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="sort_order" type="text" sortOrder="60" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Sort Order</label>
				</field>
				<field id="instructions" type="textarea" sortOrder="70" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Instructions</label>
				</field>
			</group>
		</section>
	</system>
</config>
