<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Api\Data;

interface ShippingInterface
{
    const SHIPPING_ID = 'shipping_id';
    const PICKLIST_NO = 'picklist_no';
    const SHIPMENT_ID = 'shipment_id';
    const SCANNING_TIMESTAMP = 'scanning_timestamp';
    const SHIPMENT_METHOD = 'shipment_method';
    const DELIVERY_GROUP = 'delivery_group';
    const PRODUCT_NO = 'product_no';
    const QUANTITY = 'quantity';
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const ORDER_ID = 'order_id';
    const INCREMENT_ID = 'increment_id';
    const SALES_SHIPMENT_ID = 'sales_shipment_id';

    /**
     * Get shipping_id
     * @return string|null
     */
    public function getShippingId();

    /**
     * Set shipping_id
     * @param string $shippingId
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setShippingId($shippingId);

    /**
     * Get picklist_no
     * @return string|null
     */
    public function getPicklistNo();

    /**
     * Set picklist_no
     * @param string $picklistNo
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setPicklistNo($picklistNo);

    /**
     * Get shipment_id
     * @return string|null
     */
    public function getShipmentId();

    /**
     * Set shipment_id
     * @param string $shipmentId
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setShipmentId($shipmentId);
    /**
     * Get scanning_timestamp
     * @return string|null
     */
    public function getScanningTimestamp();

    /**
     * Set scanning_timestamp
     * @param string $scanningTimestamp
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setScanningTimestamp($scanningTimestamp);
    /**
     * Get shipment_method
     * @return string|null
     */
    public function getShipmentMethod();

    /**
     * Set shipment_method
     * @param string $shipmentMethod
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setShipmentMethod($shipmentMethod);
    /**
     * Get delivery_group
     * @return string|null
     */
    public function getDeliveryGroup();

    /**
     * Set delivery_group
     * @param string $deliveryGroup
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setDeliveryGroup($deliveryGroup);
    /**
     * Get product_no
     * @return string|null
     */
    public function getProductNo();

    /**
     * Set product_no
     * @param string $productNo
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setProductNo($productNo);
    /**
     * Get quantity
     * @return string|null
     */
    public function getQuantity();

    /**
     * Set quantity
     * @param string $quantity
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setQuantity($quantity);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setCreatedAt($createdAt);
    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt();

    /**
     * Set updated_at
     * @param string $updatedAt
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setUpdatedAt($updatedAt);
    /**
     * Get order_id
     * @return string|null
     */
    public function getOrderId();

    /**
     * Set order_id
     * @param string $orderId
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setOrderId($orderId);
    /**
     * Get increment_id
     * @return string|null
     */
    public function getIncrementId();

    /**
     * Set increment_id
     * @param string $incrementId
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setIncrementId($incrementId);
    /**
     * Get sales_shipment_id
     * @return string|null
     */
    public function getSalesShipmentId();

    /**
     * Set sales_shipment_id
     * @param string $sales_shipment_id
     * @return \Fhr\Ipiccolo\Shipping\Api\Data\ShippingInterface
     */
    public function setSalesShipmentId($salesShipmentId);
}
