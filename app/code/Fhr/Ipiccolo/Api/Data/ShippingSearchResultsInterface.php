<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Api\Data;

interface ShippingSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Shipping list.
     * @return \Fhr\Ipiccolo\Api\Data\ShippingInterface[]
     */
    public function getItems();

    /**
     * Set picklist_no list.
     * @param \Fhr\Ipiccolo\Api\Data\ShippingInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
