<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface ShippingRepositoryInterface
{

    /**
     * Save Shipping
     * @param \Fhr\Ipiccolo\Api\Data\ShippingInterface $shipping
     * @return \Fhr\Ipiccolo\Api\Data\ShippingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Fhr\Ipiccolo\Api\Data\ShippingInterface $shipping
    );

    /**
     * Retrieve Shipping
     * @param string $shippingId
     * @return \Fhr\Ipiccolo\Api\Data\ShippingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($shippingId);

    /**
     * Retrieve Shipping matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Fhr\Ipiccolo\Api\Data\ShippingSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Shipping
     * @param \Fhr\Ipiccolo\Api\Data\ShippingInterface $shipping
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Fhr\Ipiccolo\Api\Data\ShippingInterface $shipping
    );

    /**
     * Delete Shipping by ID
     * @param string $shippingId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($shippingId);
}
