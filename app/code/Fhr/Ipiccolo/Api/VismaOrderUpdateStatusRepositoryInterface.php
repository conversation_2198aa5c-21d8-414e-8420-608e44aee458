<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface VismaOrderUpdateStatusRepositoryInterface
{

    /**
     * Save VismaOrderUpdateStatus
     * @param \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface $vismaOrderUpdateStatus
     * @return \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface $vismaOrderUpdateStatus
    );

    /**
     * Retrieve VismaOrderUpdateStatus
     * @param string $vismaorderupdatestatusId
     * @return \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($vismaorderupdatestatusId);

    /**
     * Retrieve VismaOrderUpdateStatus
     * @param string $orderId
     * @return \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getByOrderId($orderId);

    /**
     * Retrieve VismaOrderUpdateStatus matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete VismaOrderUpdateStatus
     * @param \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface $vismaOrderUpdateStatus
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Fhr\Ipiccolo\Api\Data\VismaOrderUpdateStatusInterface $vismaOrderUpdateStatus
    );

    /**
     * Delete VismaOrderUpdateStatus by ID
     * @param string $vismaorderupdatestatusId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($vismaorderupdatestatusId);
}
