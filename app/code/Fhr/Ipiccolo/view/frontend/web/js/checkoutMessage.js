define(
    [
        'jquery',
        'ko',
        'uiComponent',
        'Magento_Checkout/js/checkout-data',
        'Magento_Checkout/js/model/quote',
        'Magento_Ui/js/model/messageList',
        'mage/url',
    ],
    function ($, ko, Component, checkoutData, quote, messageList, url) {
        'use strict';

        return Component.extend({
            defaults: {
                template: 'Fhr_Ipiccolo/checkoutMessage',
                accountCreated: false,
                creationStarted: false,
                isFormVisible: true
            },

            initialize: function () {
                this._super();
                self = this;
                quote.shippingAddress.subscribe(function (value) {

                    // console.log('!!!HERE!!!');
                    // console.log(url.build('fhripiccolo/checkoutmessage/', {}))

                    // add you logic and compair the address.
                    if (value.postcode) {
                        $('#fhr-ipiccolo-ajaxresponse').hide();
                        $.ajax({
                            context: '#ajaxresponse',
                            url: url.build('fhripiccolo/checkoutmessage/', {}),
                            type: "POST",
                            dataType: 'json'
                        }).done(function (data) {
                            $('#ajaxresponse').html(data.output);
                            if(data.batteries) {
                                $('#fhr-ipiccolo-ajaxresponse').show();
                            }
                            return true;
                        });
                        // messageList.addErrorMessage({ message: "you custom Message" });
                    }
                }, this);
            },
        });
    }
);
