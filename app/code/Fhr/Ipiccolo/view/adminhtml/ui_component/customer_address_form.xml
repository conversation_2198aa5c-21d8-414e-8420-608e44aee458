<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
      component="Magento_Customer/js/form/components/form">
    <fieldset name="general">
        <field name="company_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
        <field name="vat_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
