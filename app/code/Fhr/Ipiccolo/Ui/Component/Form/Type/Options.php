<?php

namespace Fhr\Ipiccolo\Ui\Component\Form\Type;

use Magento\Framework\Data\OptionSourceInterface;

class Options implements OptionSourceInterface
{
    const VAL_BRANDTOCATEGORY = 'BrandToCategory';
    const LBL_BRANDTOCATEGORY = 'Brand To Category';
    const VAL_TAXCLASS = 'TaxClass';
    const LBL_TAXCLASS = 'Tax Class';
    const VAL_SHIPMENTMETHOD = 'ShipmentMethod';
    const LBL_SHIPMENTMETHOD = 'Shipment Method';

    /**
     * @return array
     */
    public function toOptionArray()
    {
        if (empty($this->_options)) {
            $options = [];
            $options[] = [
                'value' => self::VAL_BRANDTOCATEGORY,
                'label' => self::LBL_BRANDTOCATEGORY,
            ];
            $options[] = [
                'value' => self::VAL_TAXCLASS,
                'label' => self::LBL_TAXCLASS,
            ];
            $options[] = [
                'value' => self::VAL_SHIPMENTMETHOD,
                'label' => self::LBL_SHIPMENTMETHOD,
            ];
            $this->_options = $options;
        }
        return $this->_options;
    }
}
