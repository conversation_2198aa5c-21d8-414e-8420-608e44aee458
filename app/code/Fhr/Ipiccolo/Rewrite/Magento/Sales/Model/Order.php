<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Rewrite\Magento\Sales\Model;

class Order extends \Magento\Sales\Model\Order
{
    public function hasProformaInvoices()
    {
        if (in_array($this->getPayment()->getMethod(), [
            \Fhr\Ipiccolo\Model\Api\Order::PAYMENT_METHOD_BANKTRANSFER,
            \Fhr\Ipiccolo\Model\Api\Order::PAYMENT_METHOD_PAYPAL,
            \Fhr\Ipiccolo\Model\Api\Order::PAYMENT_METHOD_CARD,
            \Fhr\Ipiccolo\Model\Api\Order::PAYMENT_METHOD_FACTURA
        ])) {
            return (bool)$this->getInvoiceCollection()->count();
        }
        return false;
    }
}
