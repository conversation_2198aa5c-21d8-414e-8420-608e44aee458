<?php

namespace Fhr\Ipiccolo\Plugin\Adminhtml\Order\Invoice;

use Magento\Backend\Model\UrlInterface;
use Magento\Sales\Api\InvoiceRepositoryInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class Delete
{
    const CONTROLLER_PATH = 'fhr_ipiccolo/deleteinvoice';

    /**
     * @var UrlInterface
     */
    private $backendUrlManager;
    /**
     * @var OrderRepositoryInterface
     */
    private $invoiceRepository;

    /**
     * PluginBtnOrderView constructor.
     * @param UrlInterface $backendUrlManager
     * @param OrderRepositoryInterface $invoiceRepository
     */
    public function __construct(
        UrlInterface $backendUrlManager,
        InvoiceRepositoryInterface $invoiceRepository
    ) {
        $this->backendUrlManager = $backendUrlManager;
        $this->invoiceRepository = $invoiceRepository;
    }

    /**
     * @param \Magento\Sales\Block\Adminhtml\Order\Invoice\View $subject
     * @return null
     */
    public function beforeSetLayout(\Magento\Sales\Block\Adminhtml\Order\Invoice\View $subject)
    {
        $invoice = $subject->getInvoice();
        $message = __('Do you really want to delete Invoice ? [ID:%1]', $invoice->getId());
        $subject->addButton(
            'fhr_ipiccolo_deleteinvoice',
            [
                'label' => __('Delete'),
                'onclick' => "confirmSetLocation('{$message}', '{$this->getUrlButton(['invoice_id' => $invoice->getId()])}')"
            ]
        );
        return null;
    }

    /**
     * @param array $params
     * @return string
     */
    private function getUrlButton($params = [])
    {
        return $this->backendUrlManager->getUrl(self::CONTROLLER_PATH, $params);
    }
}
