<?php

namespace Fhr\Ipiccolo\Console\Command;

use Fhr\Ipiccolo\Model\Api\VatValidation;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class VatValidationCustomer extends Command
{
    const ARGUMENT_CUSTOMER_ID = 'customerid';

    /**
     * @var VatValidation
     */
    private $vatValidation;
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @param VatValidation $vatValidation
     * @param CustomerRepositoryInterface $customerRepository
     * @param string|null $name
     */
    public function __construct(
        VatValidation $vatValidation,
        CustomerRepositoryInterface $customerRepository,
        string $name = null
    ) {
        $this->vatValidation = $vatValidation;
        $this->customerRepository = $customerRepository;
        parent::__construct($name);
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $customerId = $input->getArgument(self::ARGUMENT_CUSTOMER_ID);
        $customer = $this->customerRepository->getById($customerId);
        if ($this->vatValidation->validateVat($customer)) {
            $output->writeln(
                __(
                    'Customer %1 %2 (%3) ID: %4 VAT %5 Valid',
                    $customer->getFirstname(),
                    $customer->getLastname(),
                    $customer->getEmail(),
                    $customerId,
                    $customer->getTaxvat()
                )
            );
        } else {
            $output->writeln(
                __(
                    'Customer %1 %2 (%3) ID: %4 VAT %5 Not valid',
                    $customer->getFirstname(),
                    $customer->getLastname(),
                    $customer->getEmail(),
                    $customerId,
                    $customer->getTaxvat()
                )
            );
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName('fhr_ipiccolo:customervatvalidate');
        $this->setDescription('Customer validate VAT by ID');
        $this->setDefinition([
            new InputArgument(self::ARGUMENT_CUSTOMER_ID, InputArgument::REQUIRED, 'Customer ID')
        ]);
        parent::configure();
    }
}
