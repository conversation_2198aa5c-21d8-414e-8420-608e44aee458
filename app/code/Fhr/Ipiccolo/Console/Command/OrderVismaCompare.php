<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Console\Command;

use Fhr\Ipiccolo\Model\Order\OrderVismaServices;
use Magento\Sales\Api\OrderRepositoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class OrderVismaCompare extends Command
{

    const OPTION_FROM = 'from';
    const OPTION_TO = 'to';
    const OPTION_ORDER_ID = 'orderid';

    /**
     * @var OrderVismaServices
     */
    private $orderVismaServices;
    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param OrderVismaServices $orderVismaServices
     * @param string|null $name
     */
    public function __construct(
        OrderRepositoryInterface $orderRepository,
        OrderVismaServices $orderVismaServices,
        string $name = null
    ) {
        $this->orderRepository = $orderRepository;
        $this->orderVismaServices = $orderVismaServices;
        parent::__construct($name);
    }

    /**
     * {@inheritdoc}
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $from = $input->getOption(self::OPTION_FROM);
        $to = $input->getOption(self::OPTION_TO);
        $orderId = $input->getOption(self::OPTION_ORDER_ID);
        if (!empty($from) && !empty($to)) {
            $from = date('Y-m-d 00:00:00', strtotime($from));
            $to = date('Y-m-d 23:59:59', strtotime($to));
            $output->writeln(__('Compare magento orders with visma from %1, to %2', $from, $to));
            $this->orderVismaServices->compareMagentoVismaDateRange($from, $to);
        }
        if (!empty($orderId)) {
            $order = $this->orderRepository->get($orderId);
            if (!empty($order->getEntityId())) {
                $output->writeln(
                    __('Compare magento order #%1[%2] with visma ', $order->getIncrementId(), $order->getEntityId())
                );
                $this->orderVismaServices->compareMagentoVismaOrder($order);
            } else {
                $output->writeln(
                    __('Compare magento order ID: %1 with visma. Order not found.', $orderId)
                );
            }
        }
        $output->writeln(__('Compare DONE!'));
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("fhr_ipiccolo:ordervismacompare");
        $this->setDescription("Compare magento orders with visma");
        $this->setDefinition([
            new InputOption(self::OPTION_FROM, null, InputOption::VALUE_OPTIONAL, 'Date range from YYYY-MM-DD'),
            new InputOption(self::OPTION_TO, null, InputOption::VALUE_OPTIONAL, 'Date range to YYYY-MM-DD'),
            new InputOption(self::OPTION_ORDER_ID, null, InputOption::VALUE_OPTIONAL, 'Order ID')
        ]);
        parent::configure();
    }
}
