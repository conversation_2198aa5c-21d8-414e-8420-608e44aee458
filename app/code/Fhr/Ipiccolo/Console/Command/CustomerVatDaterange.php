<?php
declare(strict_types=1);

namespace Fhr\Ipiccolo\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class CustomerVatDaterange extends Command
{

    const ARGUMENT_FROM = 'from';
    const ARGUMENT_TO = 'to';

    /**
     * @var \Fhr\Ipiccolo\Model\Api\Customer
     */
    private $ipiccoloApiCustomer;

    /**
     * @param \Fhr\Ipiccolo\Model\Api\Customer $ipiccoloApiCustomer
     * @param string|null $name
     */
    public function __construct(
        \Fhr\Ipiccolo\Model\Api\Customer $ipiccoloApiCustomer,
        string                           $name = null
    ) {
        parent::__construct($name);
        $this->ipiccoloApiCustomer = $ipiccoloApiCustomer;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $from = $input->getArgument(self::ARGUMENT_FROM);
        $to = $input->getArgument(self::ARGUMENT_TO);
        $customerVatDataArr = (array)$this->ipiccoloApiCustomer->getModifiedCustomerVatList($from, $to);
        foreach ($customerVatDataArr as $customerVatData) {
            $customerId = $this->ipiccoloApiCustomer->customerNumberToCustomerId($customerVatData['CustomerNumber']);
            $output->writeln(__(
                'Try get customer VAT for customer ID: %1, CustomerNumber: %2 from Ipiccolo',
                $customerId,
                $customerVatData['CustomerNumber']
            ));
            $this->ipiccoloApiCustomer->syncCustomerVat($customerId);
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName('fhr_ipiccolo:customervatdaterange');
        $this->setDescription('Customer VAT/OrgNum sync date range');
        $this->setDefinition([
            new InputArgument(self::ARGUMENT_FROM, InputArgument::REQUIRED, 'Date range from YYYY-MM-DD'),
            new InputArgument(self::ARGUMENT_TO, InputArgument::REQUIRED, 'Date range to YYYY-MM-DD')
        ]);
        parent::configure();
    }
}
