<?php
declare(strict_types=1);

namespace Fhr\Ipiccolo\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class StockAvailabilitySync extends Command
{

    const ARGUMENT_PRODUCT_NO = 'productno';

    /**
     * @var \Fhr\Ipiccolo\Model\Api\StockAvailability
     */
    private $ipiccoloStockAvailability;

    /**
     * @param \Fhr\Ipiccolo\Model\Api\StockAvailability $ipiccoloStockAvailability
     * @param string|null $name
     */
    public function __construct(
        \Fhr\Ipiccolo\Model\Api\StockAvailability $ipiccoloStockAvailability,
        string $name = null
    ) {
        parent::__construct($name);
        $this->ipiccoloStockAvailability = $ipiccoloStockAvailability;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $productNo = $input->getArgument(self::ARGUMENT_PRODUCT_NO);
        $this->ipiccoloStockAvailability->syncStockAvailability($productNo);
        $output->writeln(__('Try sync stock availability for productNo %1 from Ipiccolo', $productNo));
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName('fhr_ipiccolo:stockavailabilitysync');
        $this->setDescription('Stock availability sync by ProductNo');
        $this->setDefinition([
            new InputArgument(self::ARGUMENT_PRODUCT_NO, InputArgument::REQUIRED, 'ProductNo')
        ]);
        parent::configure();
    }
}
