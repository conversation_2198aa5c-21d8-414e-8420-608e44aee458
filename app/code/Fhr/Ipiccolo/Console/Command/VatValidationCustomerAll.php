<?php

namespace Fhr\Ipiccolo\Console\Command;

use Fhr\Ipiccolo\Model\Api\VatValidation;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class VatValidationCustomerAll extends Command
{
    /**
     * @var VatValidation
     */
    private $vatValidation;
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @param VatValidation $vatValidation
     * @param CustomerRepositoryInterface $customerRepository
     * @param string|null $name
     */
    public function __construct(
        VatValidation $vatValidation,
        CustomerRepositoryInterface $customerRepository,
        string $name = null
    ) {
        $this->vatValidation = $vatValidation;
        $this->customerRepository = $customerRepository;
        parent::__construct($name);
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $output->writeln(__('BEGIN Validate VAT for all customers ...'));
        $this->vatValidation->validateAll();
        $output->writeln(__('FINISH Validate VAT for all customers ...'));
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName('fhr_ipiccolo:customervatvalidateall');
        $this->setDescription('Customer validate VAT All Activated[customer_activated=1] Customers');
        $this->setDefinition([
        ]);
        parent::configure();
    }
}
