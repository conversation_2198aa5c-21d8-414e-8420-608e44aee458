<?php

namespace Fhr\Ipiccolo\Cron;

use Fhr\Ipiccolo\Model\Api\Customer as IpiccoloApiCustomer;
use Fhr\Ipiccolo\Model\Api\Price as IpiccoloApiPrice;
use Fhr\Ipiccolo\Model\Api\Product as IpiccoloApiProduct;
use Fhr\Ipiccolo\Model\Api\StockAvailability as IpiccoloApiStockAvailability;
use Fhr\Ipiccolo\Model\Api\VatValidation;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;

class EndDay
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var VatValidation
     */
    private $vatValidation;
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;
    /**
     * @var TimezoneInterface
     */
    private $timezone;
    /**
     * @var IpiccoloApiCustomer
     */
    private $ipiccoloApiCustomer;
    /**
     * @var IpiccoloApiProduct
     */
    private $ipiccoloApiProduct;
    /**
     * @var IpiccoloApiStockAvailability
     */
    private $ipiccoloApiStockAvailability;
    /**
     * @var IpiccoloApiPrice
     */
    private $ipiccoloApiPrice;
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;
    /**
     * @var \Fhr\Ipiccolo\Model\Api\Billings
     */
    private $billings;

    /**
     * @param \Fhr\Ipiccolo\Model\Api\Billings $billings
     * @param ResourceConnection $resourceConnection
     * @param IpiccoloApiPrice $ipiccoloApiPrice
     * @param IpiccoloApiStockAvailability $ipiccoloApiStockAvailability
     * @param IpiccoloApiProduct $ipiccoloApiProduct
     * @param IpiccoloApiCustomer $ipiccoloApiCustomer
     * @param TimezoneInterface $timezone
     * @param VatValidation $vatValidation
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        \Fhr\Ipiccolo\Model\Api\Billings $billings,
        ResourceConnection $resourceConnection,
        IpiccoloApiPrice $ipiccoloApiPrice,
        IpiccoloApiStockAvailability $ipiccoloApiStockAvailability,
        IpiccoloApiProduct $ipiccoloApiProduct,
        IpiccoloApiCustomer $ipiccoloApiCustomer,
        TimezoneInterface $timezone,
        VatValidation $vatValidation,
        ScopeConfigInterface $scopeConfig,
        LoggerInterface $logger
    ) {
        $this->billings = $billings;
        $this->resourceConnection = $resourceConnection;
        $this->ipiccoloApiPrice = $ipiccoloApiPrice;
        $this->ipiccoloApiStockAvailability = $ipiccoloApiStockAvailability;
        $this->ipiccoloApiProduct = $ipiccoloApiProduct;
        $this->ipiccoloApiCustomer = $ipiccoloApiCustomer;
        $this->timezone = $timezone;
        $this->vatValidation = $vatValidation;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
    }

    /**
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        $to = $this->timezone->date()->format('Y-m-d 23:59:59');
        $from = $this->timezone->date()->modify('-2 days')->format('Y-m-d 00:00:00');

        // Vat Validation All Customers
        $this->logger->info(__('BEGIN Validate VAT for all customers %1 - %2', $from, $to));
        $this->vatValidation->validateAll();
        $this->logger->info(__('END Validate VAT for all customers'));
        // VISMA Customer
        if ($this->scopeConfig->getValue(\Fhr\Ipiccolo\Cron\CustomerVat::CONFIG_CUSTOMERVAT)) {
            $this->logger->info(__('BEGIN VISMA Customer sync'));
            $customerVatDataArr = (array)$this->ipiccoloApiCustomer->getModifiedCustomerVatList($from, $to);
            foreach ($customerVatDataArr as $customerVatData) {
                $customerId = $this->ipiccoloApiCustomer->customerNumberToCustomerId($customerVatData['CustomerNumber']);
                $this->ipiccoloApiCustomer->syncCustomerVat($customerId);
            }
            $this->logger->info(__('END VISMA Customer sync'));
        } else {
            $this->logger->info(__('Ipiccolo Cron[fhr_ipiccolo_cron_customervat] - disabled'));
        }
        // VISMA Product
        if ($this->scopeConfig->getValue(\Fhr\Ipiccolo\Cron\Product::CONFIG_SYNCPRODUCTS)) {
            $this->logger->info(__('BEGIN VISMA Product %1 - %2', $from, $to));
            $productDataArray = $this->ipiccoloApiProduct->getModifiedProductsList($from, $to);
            foreach ($productDataArray as $productData) {
                $this->ipiccoloApiProduct->syncProduct($productData);
            }
            $this->logger->info(__('END VISMA Product'));
        } else {
            $this->logger->info(__('Ipiccolo Cron[fhr_ipiccolo_cron_product] - disabled'));
        }
        // VISMA Stock Availability
        if ($this->scopeConfig->getValue(\Fhr\Ipiccolo\Cron\StockAvailability::CONFIG_SYNCSTOCKAVAILABILITY)) {
            $this->logger->info(__('BEGIN VISMA Stock Availability %1 - %2', $from, $to));
            $this->ipiccoloApiStockAvailability->syncStockAvailabilityModified($from, $to);
            $this->logger->info(__('END VISMA Stock Availability'));

        } else {
            $this->logger->info(__('Ipiccolo Cron[fhr_ipiccolo_cron_stockavailability] - disabled'));
        }
        // VISMA Price
        if ($this->scopeConfig->getValue(\Fhr\Ipiccolo\Cron\Price::CONFIG_SYNCPRICE)) {
            $this->logger->info(__('BEGIN VISMA Price %1 - %2', $from, $to));
            $this->ipiccoloApiPrice->syncPriceModified($from, $to);
            $this->logger->info(__('END VISMA Price'));
        } else {
            $this->logger->info(__('Ipiccolo Cron[fhr_ipiccolo_cron_price] - disabled'));
        }
        // TRUNCATE fhr_ipiccolo_vismaorderupdatestatus
        try {
            $connection = $this->resourceConnection->getConnection();
            $table = $connection->getTableName('fhr_ipiccolo_vismaorderupdatestatus');
            $connection->truncateTable($table);
            $this->logger->info(__('Ipiccolo Cron[fhr_ipiccolo_cron_price] - %1 TRUNCATED', $table));
        } catch (\Exception $exception) {
            $this->logger->info(__(
                'Ipiccolo Cron[fhr_ipiccolo_cron_price] - %1 NOT TRUNCATED, ERROR %2',
                $table,
                $exception->getMessage()
            ));
        }
        // Download pdf Invoices
        $this->logger->info(__('BEGIN Download pdf Invoices %1 - %2', $from, $to));
        $this->billings->syncBillingsModified($from, $to);
        $this->logger->info(__('END Download pdf Invoices'));
        // Final log entry
        $this->logger->info(__('Ipiccolo Cron[fhr_ipiccolo_cron_endday] - executed'));
    }
}
