<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Model;

use Fhr\Ipiccolo\Api\Data\VismaDataInterface;
use Magento\Framework\Model\AbstractModel;

class VismaData extends AbstractModel implements VismaDataInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Fhr\Ipiccolo\Model\ResourceModel\VismaData::class);
    }

    /**
     * @inheritDoc
     */
    public function getVismadataId()
    {
        return $this->getData(self::VISMADATA_ID);
    }

    /**
     * @inheritDoc
     */
    public function setVismadataId($vismadataId)
    {
        return $this->setData(self::VISMADATA_ID, $vismadataId);
    }

    /**
     * @inheritDoc
     */
    public function getVismaOrderNo()
    {
        return $this->getData(self::VISMA_ORDER_NO);
    }

    /**
     * @inheritDoc
     */
    public function setVismaOrderNo($vismaOrderNo)
    {
        return $this->setData(self::VISMA_ORDER_NO, $vismaOrderNo);
    }

    /**
     * @inheritDoc
     */
    public function setOrderId($orderId)
    {
        return $this->setData(self::ORDER_ID, $orderId);
    }

    /**
     * @inheritDoc
     */
    public function getOrderId()
    {
        return $this->getData(self::ORDER_ID);
    }
}
