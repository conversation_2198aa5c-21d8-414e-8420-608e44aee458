<?php

namespace Fhr\Ipiccolo\Model\Config\Source;

class Syncordertype implements \Magento\Framework\Option\ArrayInterface
{
    const TYPE_OLD = 'old';
    const TYPE_OLD_LBL = 'Old syncronisation mode (from 2022 feb)';
    const TYPE_NEW = 'new';
    const TYPE_NEW_LBL = 'New syncronisation';

    public function toOptionArray()
    {
        return [
            ['value' => self::TYPE_OLD, 'label' => __(self::TYPE_OLD_LBL)],
            ['value' => self::TYPE_NEW, 'label' => __(self::TYPE_NEW_LBL)],
        ];
    }

    public function toArray()
    {
        return [
            self::TYPE_OLD => __(self::TYPE_OLD_LBL),
            self::TYPE_NEW => __(self::TYPE_NEW_LBL),
        ];
    }
}
