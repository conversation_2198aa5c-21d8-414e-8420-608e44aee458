<?php

namespace Fhr\Ipiccolo\Model\Api;

use Fhr\Base\Model\Common;
use Fhr\Ipiccolo\Api\BillingsRepositoryInterface;
use Fhr\Ipiccolo\Api\Data\BillingsInterfaceFactory;
use Fhr\Ipiccolo\Model\Api\Order as IpiccoloApiOrder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Driver\Https;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\HTTP\ClientInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class Billings
{
    const DIR_INVOICE_FOLDER = '/ipiccolo/billings/';
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var Order
     */
    private $ipiccoloApiOrder;
    /**
     * @var Common
     */
    private $common;
    /**
     * @var Filesystem
     */
    private $filesystem;
    /**
     * @var Filesystem\Driver\File
     */
    private $fileDriver;
    /**
     * @var DirectoryList
     */
    private $directoryList;
    /**
     * @var File
     */
    private $ioFile;
    /**
     * @var Https
     */
    private $clientInterface;
    /**
     * @var BillingsRepositoryInterface
     */
    private $billingsRepository;
    /**
     * @var BillingsInterfaceFactory
     */
    private $billingsFactory;
    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param OrderRepositoryInterface $orderRepository
     * @param BillingsInterfaceFactory $billingsFactory
     * @param BillingsRepositoryInterface $billingsRepository
     * @param File $ioFile
     * @param DirectoryList $directoryList
     * @param Filesystem\Driver\File $fileDriver
     * @param ClientInterface $clientInterface
     * @param Filesystem $filesystem
     * @param Common $common
     * @param Order $ipiccoloApiOrder
     * @param LoggerInterface $logger
     */
    public function __construct(
        SearchCriteriaBuilder $searchCriteriaBuilder,
        OrderRepositoryInterface $orderRepository,
        BillingsInterfaceFactory                $billingsFactory,
        BillingsRepositoryInterface             $billingsRepository,
        File                                    $ioFile,
        DirectoryList                           $directoryList,
        Filesystem\Driver\File                  $fileDriver,
        ClientInterface $clientInterface,
        Filesystem                              $filesystem,
        Common                                  $common,
        IpiccoloApiOrder                        $ipiccoloApiOrder,
        LoggerInterface                         $logger
    ) {
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->orderRepository = $orderRepository;
        $this->billingsFactory = $billingsFactory;
        $this->billingsRepository = $billingsRepository;
        $this->clientInterface = $clientInterface;
        $this->ioFile = $ioFile;
        $this->directoryList = $directoryList;
        $this->fileDriver = $fileDriver;
        $this->filesystem = $filesystem;
        $this->common = $common;
        $this->ipiccoloApiOrder = $ipiccoloApiOrder;
        $this->logger = $logger;
    }

    /**
     * @param array $billing
     * @param OrderInterface $order
     * @return bool
     * @throws LocalizedException
     */
    public function saveBilling(array $billing, OrderInterface $order): bool
    {
        if ($this->saveFile($billing)) {
            if ($this->saveDB($billing, $order)) {
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * @param OrderInterface $order
     * @return array|mixed
     */
    public function getBillingsByOrder($order)
    {
        $orderStatusData = $this->ipiccoloApiOrder->getOrderStatus($order);
        return $this->getBillingsFromStatusData($orderStatusData);
    }

    /**
     * @param array $orderStatusData
     * @return array
     */
    public function getBillingsFromStatusData(array $orderStatusData): array
    {
        if ($this->common->arrayKeyExistsMulti($orderStatusData, 'Billings')) {
            return (array)$orderStatusData['OrderStatusData']['Billings'];
        }
        return [];
    }

    /**
     * @param array $billing
     * @return bool|false
     */
    public function saveFile(array $billing): bool
    {
        $fileName = $billing['InvoiceFile'];
        if (empty($fileData = $this->getFileContent($billing['InvoiceUrl']))) {
            return false;
        }
        try {
            $path = $this->getPathInvoice();
            if (!$this->isExists($path)) { // Create folder if not exist
                $this->ioFile->mkdir($path, 0775);
            }
            $writeDirectory = $this->filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::VAR_DIR);
            $stream = $writeDirectory->openFile($path.$fileName, 'w+');
            $stream->lock();
            $stream->write($fileData);
            $stream->unlock();
            $stream->close();
            return true;
        } catch (\Exception $e) {
            $this->logger->error(__('[Ipiccolo][Billings] Can\'t save file %1 Error: %2', $fileName, $e->getMessage()));
            return false;
        }
    }


    /**
     * @param array $billing
     * @param OrderInterface $order
     * @return bool
     * @throws LocalizedException
     */
    public function saveDB(array $billing, OrderInterface $order)
    {
        $billingEntity = $this->billingsRepository->getByinvoiceNo($billing['InvoiceNo']);
        if (empty($billingEntity)) {
            $billingEntity = $this->billingsFactory->create();
        }
        $billingEntity->setInvoiceNo($billing['InvoiceNo']);
        $billingEntity->setInvoiceFile($billing['InvoiceFile']);
        $billingEntity->setInvoiceUrl($billing['InvoiceUrl']);
        $billingEntity->setOrderId($order->getEntityId());
        $billingEntity->setIncrementId($order->getIncrementId());
        try {
            $this->billingsRepository->save($billingEntity);
            return true;
        } catch (\Exception $e) {
            $this->logger->error(
                __(
                    '[Ipiccolo][Billings] Can\'t save to DB Order ID: #%1[%2], Invoice No %3 Error: %4',
                    $order->getIncrementId(),
                    $order->getEntityId(),
                    $billing['InvoiceNo'],
                    $e->getMessage()
                )
            );
            return false;
        }
    }

    public function isExists($path)
    {
        return $this->fileDriver->isExists($path);
    }

    /**
     * @return array|string|string[]|null
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function getPathInvoice()
    {
        return $this->directoryList->getPath(\Magento\Framework\App\Filesystem\DirectoryList::VAR_DIR)
            . self::DIR_INVOICE_FOLDER;
    }

    /**
     * @param $url
     * @return false|string
     */
    public function getFileContent($url)
    {
        try {
            $this->clientInterface->get($url);
            if ($this->clientInterface->getStatus() == 200) {
                return $this->clientInterface->getBody();
            }
            $this->logger->error(
                __(
                    '[Ipiccolo][Billings] Not Succcess STATUS %2. For URL %1, Body: %3',
                    $url,
                    $this->clientInterface->getStatus(),
                    $this->clientInterface->getBody()
                )
            );
            return false;
        } catch (\Exception $e) {
            $this->logger->error(
                __('[Ipiccolo][Billings] Can\'t get content from file %1 Error: %2', $url, $e->getMessage())
            );
            return false;
        }
    }

    /**
     * @param string $from <p>Format YYYY-MM-DD HH:II:SS</p>
     * @param string $to <p>Format YYYY-MM-DD HH:II:SS</p>
     * @return void
     * @throws LocalizedException
     */
    public function syncBillingsModified(string $from, string $to)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('created_at', $from, 'gteq')
            ->addFilter('created_at', $to, 'lteq')
            ->addFilter('entity_id', \Fhr\Ipiccolo\Model\Api\Order::OFFSET_WEB_ORDER_NUMBER, 'gt')
            ->create();
        $orders = $this->orderRepository->getList($searchCriteria)->getItems();
        foreach ($orders as $order) {
            $billings = $this->getBillingsByOrder($order);
            foreach ($billings as $billing) {
                $this->saveBilling($billing, $order);
            }
        }
    }
}
