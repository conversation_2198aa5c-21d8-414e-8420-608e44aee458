<?php

namespace Fhr\Ipiccolo\Model\Api;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Psr\Log\LoggerInterface;

class StockAvailability
{
    const DATA_ARRAY_ROOT_KEY = 'StockAvailabilityData';
    const DISABLE_PRODUCT_STATUSES = ['Utgangen'];

    /**
     * @var Ipiccolo
     */
    private $ipiccolo;
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var StockRegistryInterface
     */
    private $stockRegistry;

    /**
     * @param Ipiccolo $ipiccolo
     * @param ProductRepositoryInterface $productRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param StockRegistryInterface $stockRegistry
     * @param LoggerInterface $logger
     */
    public function __construct(
        Ipiccolo $ipiccolo,
        ProductRepositoryInterface $productRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        StockRegistryInterface $stockRegistry,
        LoggerInterface $logger
    ) {
        $this->ipiccolo = $ipiccolo;
        $this->productRepository = $productRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->stockRegistry = $stockRegistry;
        $this->logger = $logger;
    }

    /**
     * @param string $productNo
     * @return array|null
     */
    public function getStockAvailability($productNo, $getFull=false)
    {
        $params['ProductNo'] = $productNo;
        $result = $this->ipiccolo->getStockAvailability($params);
        if($getFull) {
            return $result;
        }
        return $result[self::DATA_ARRAY_ROOT_KEY][0];
    }

    /**
     * @param $productNo
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function syncStockAvailability($productNo)
    {
        $searchCriteria = $this->searchCriteriaBuilder->addFilter('sku', $productNo)->create();
        $items = $this->productRepository->getList($searchCriteria)->getItems();
        $stockAvailabilityDataFull = $this->getStockAvailability($productNo, true);
        if($stockAvailabilityDataFull['ResponseResult']['Code'] == 'FAILED') {
            $this->logger->info(
                __('Ipiccolo sync stock availability for productNo = %1, ResponseResult Code = FAILED', $productNo)
            );
            if(
                isset($stockAvailabilityDataFull['ResponseResult']['Description'])
                && $stockAvailabilityDataFull['ResponseResult']['Description'] == 'Product or stock availability not found'
            ) {
                // Disable not exists product in magento
                foreach ($items as $item) {
                    $this->disableProduct($item);
                }
            }
        } else {
            $stockAvailabilityData = $stockAvailabilityDataFull[self::DATA_ARRAY_ROOT_KEY][0];
            $this->logger->info(__('Ipiccolo sync stock availability for productNo = %1, stockAvailabilityData = %2',
                $productNo, print_r($stockAvailabilityData, true)));
            foreach ($items as $item) {
                $stockItem = $this->stockRegistry->getStockItemBySku($item->getSku());
                $stockItem->setQty($stockAvailabilityData['UnitsInStock']);
                $stockItem->setIsInStock(1);
                $this->stockRegistry->updateStockItemBySku($item->getSku(), $stockItem);
                // Disable product in magento by ERP status
                if(in_array($stockAvailabilityData['ProductStatus'], self::DISABLE_PRODUCT_STATUSES)) {
                    $this->disableProduct($item);
                }
            }
        }
    }

    /**
     * @param $sku
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function syncStockAvailabilityBySku($sku)
    {
//        $product = $this->productRepository->get($sku);
        $productNo = $sku;
        if(!empty($productNo)) {
            $this->syncStockAvailability($productNo);
        }
    }

    /**
     * @param $from
     * @param $to
     * @return array
     */
    public function getModifiedStockAvailabilityList($from, $to) {
        $params['FromDateTime'] = $from;
        $params['ToDateTime'] = $to;
        return (array)$this->ipiccolo->searchModifiedStockAvailability($params)[self::DATA_ARRAY_ROOT_KEY];
    }

    /**
     * @param string $from
     * @param string $to
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function syncStockAvailabilityModified($from, $to)
    {
        $stockAvailabilityDataArray = $this->getModifiedStockAvailabilityList($from, $to);
        if (!empty($stockAvailabilityDataArray)) {
            $total = count($stockAvailabilityDataArray);
            $i = 0;
            $this->logger->info(__('%1 => %2[%3] Total items to update %4',
                __CLASS__,
                __FUNCTION__,
                __LINE__,
                $total
            ));
            foreach ($stockAvailabilityDataArray as $row) {
                $i++;
                $this->logger->info(__('%1 => %2[%3] Try update item step %4/%5 ROW: %6',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $i,
                    $total,
                    print_r($row, true)
                ));
                $sku = $row['ProductNo'];
                try {
                    $product = $this->productRepository->get($sku);
                } catch (\Exception $e) {
                    $this->logger->warning(__('%1 => %2[%3] Product not exists SKU: %4. ERROR %5',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $sku,
                        $e->getMessage()
                    ));
                    continue;
                }
                $qty = $row['UnitsInStock'];
                $productStatus = $row['ProductStatus'];
                $stockItem = $this->stockRegistry->getStockItemBySku($sku);
                $stockItem->setQty($qty);
                $stockItem->setIsInStock(1);
                try {
                    // Disable product in magento by ERP status
                    if(in_array($productStatus, self::DISABLE_PRODUCT_STATUSES)) {
                        $this->disableProduct($product);
                    }
                    $stockItemId = $this->stockRegistry->updateStockItemBySku($sku, $stockItem);
                    if (!empty($stockItemId)) {
                        $this->logger->info(
                            __('%1 => %2[%3] Saved Stock Item ID: %4 SKU: %5 Qty: %6 StockAvailabilityData ROW: %7',
                                __CLASS__,
                                __FUNCTION__,
                                __LINE__,
                                $stockItemId,
                                $sku,
                                $qty,
                                print_r($row, true)
                            ));
                    } else {
                        $this->logger->warning(
                            __('%1 => %2[%3] NOT SAVED Stock Item SKU: %4 Qty: %5 StockAvailabilityData ROW: %6',
                                __CLASS__,
                                __FUNCTION__,
                                __LINE__,
                                $sku,
                                $qty,
                                print_r($row, true)
                            ));
                    }
                } catch (\Exception $e) {
                    $this->logger->warning(
                        __('%1 => %2[%3] NOT SAVED Stock Item SKU: %4 Qty: %5, ERROR: %6, StockAvailabilityData ROW: %7',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $sku,
                            $qty,
                            $e->getMessage(),
                            print_r($row, true)
                        ));
                }
            }
        }
    }

    /**
     * @param \Magento\Catalog\Api\Data\ProductInterface $item
     * @return void
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function disableProduct(\Magento\Catalog\Api\Data\ProductInterface $item)
    {
        if($item->getStatus() !== \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED) {
            try {

                $this->logger->info(
                    __('Ipiccolo sync stock availability for productNo = %1, Try disable product', $item->getSku())
                );
                $productId = $item->getId();
                $item->setStoreId(0); // Set the store ID to 0 for the admin store
                // Load the product with all store data
                $product = $this->productRepository->getById($productId, false, $item->getStoreId());
                $product->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED);
                // Save the product
                $this->productRepository->save($product);
                $this->logger->info(
                    __('Ipiccolo sync stock availability for productNo = %1, Product disabled', $item->getSku())
                );
            } catch (\Exception $exception) {
                $this->logger->error(__('Ipiccolo sync stock availability for productNo = %1, Exception on try disable product %2',
                    $item->getSku(), $exception->getMessage()));
            }
        }
    }
}
