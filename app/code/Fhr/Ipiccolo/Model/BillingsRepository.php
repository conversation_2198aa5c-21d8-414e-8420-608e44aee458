<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Model;

use Fhr\Ipiccolo\Api\BillingsRepositoryInterface;
use Fhr\Ipiccolo\Api\Data\BillingsInterface;
use Fhr\Ipiccolo\Api\Data\BillingsInterfaceFactory;
use Fhr\Ipiccolo\Api\Data\BillingsSearchResultsInterfaceFactory;
use Fhr\Ipiccolo\Model\ResourceModel\Billings as ResourceBillings;
use Fhr\Ipiccolo\Model\ResourceModel\Billings\CollectionFactory as BillingsCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class BillingsRepository implements BillingsRepositoryInterface
{

    /**
     * @var ResourceBillings
     */
    protected $resource;

    /**
     * @var BillingsInterfaceFactory
     */
    protected $billingsFactory;

    /**
     * @var BillingsCollectionFactory
     */
    protected $billingsCollectionFactory;

    /**
     * @var Billings
     */
    protected $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;


    /**
     * @param ResourceBillings $resource
     * @param BillingsInterfaceFactory $billingsFactory
     * @param BillingsCollectionFactory $billingsCollectionFactory
     * @param BillingsSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceBillings $resource,
        BillingsInterfaceFactory $billingsFactory,
        BillingsCollectionFactory $billingsCollectionFactory,
        BillingsSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->billingsFactory = $billingsFactory;
        $this->billingsCollectionFactory = $billingsCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(BillingsInterface $billings)
    {
        try {
            $this->resource->save($billings);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the billings: %1',
                $exception->getMessage()
            ));
        }
        return $billings;
    }

    /**
     * @inheritDoc
     */
    public function get($billingsId)
    {
        $billings = $this->billingsFactory->create();
        $this->resource->load($billings, $billingsId);
        if (!$billings->getId()) {
            throw new NoSuchEntityException(__('Billings with id "%1" does not exist.', $billingsId));
        }
        return $billings;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->billingsCollectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(BillingsInterface $billings)
    {
        try {
            $billingsModel = $this->billingsFactory->create();
            $this->resource->load($billingsModel, $billings->getBillingsId());
            $this->resource->delete($billingsModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Billings: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($billingsId)
    {
        return $this->delete($this->get($billingsId));
    }

    /**
     * @inheritDoc
     */
    public function getByinvoiceNo($invoiceNo)
    {
        $billingsModel = $this->billingsFactory->create();
        $this->resource->load($billingsModel, $invoiceNo, 'invoice_no');
        return $billingsModel;
    }
}
