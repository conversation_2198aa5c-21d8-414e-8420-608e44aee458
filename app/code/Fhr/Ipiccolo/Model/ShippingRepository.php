<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Model;

use Fhr\Ipiccolo\Api\Data\ShippingInterface;
use Fhr\Ipiccolo\Api\Data\ShippingInterfaceFactory;
use Fhr\Ipiccolo\Api\Data\ShippingSearchResultsInterfaceFactory;
use Fhr\Ipiccolo\Api\ShippingRepositoryInterface;
use Fhr\Ipiccolo\Model\ResourceModel\Shipping as ResourceShipping;
use Fhr\Ipiccolo\Model\ResourceModel\Shipping\CollectionFactory as ShippingCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class ShippingRepository implements ShippingRepositoryInterface
{

    /**
     * @var ResourceShipping
     */
    protected $resource;

    /**
     * @var ShippingInterfaceFactory
     */
    protected $shippingFactory;

    /**
     * @var ShippingCollectionFactory
     */
    protected $shippingCollectionFactory;

    /**
     * @var Shipping
     */
    protected $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;


    /**
     * @param ResourceShipping $resource
     * @param ShippingInterfaceFactory $shippingFactory
     * @param ShippingCollectionFactory $shippingCollectionFactory
     * @param ShippingSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceShipping $resource,
        ShippingInterfaceFactory $shippingFactory,
        ShippingCollectionFactory $shippingCollectionFactory,
        ShippingSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->shippingFactory = $shippingFactory;
        $this->shippingCollectionFactory = $shippingCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(ShippingInterface $shipping)
    {
        try {
            $this->resource->save($shipping);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the shipping: %1',
                $exception->getMessage()
            ));
        }
        return $shipping;
    }

    /**
     * @inheritDoc
     */
    public function get($shippingId)
    {
        $shipping = $this->shippingFactory->create();
        $this->resource->load($shipping, $shippingId);
        if (!$shipping->getId()) {
            throw new NoSuchEntityException(__('Shipping with id "%1" does not exist.', $shippingId));
        }
        return $shipping;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->shippingCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(ShippingInterface $shipping)
    {
        try {
            $shippingModel = $this->shippingFactory->create();
            $this->resource->load($shippingModel, $shipping->getShippingId());
            $this->resource->delete($shippingModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Shipping: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($shippingId)
    {
        return $this->delete($this->get($shippingId));
    }
}
