<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Model\ResourceModel\VismaData;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'vismadata_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Fhr\Ipiccolo\Model\VismaData::class,
            \Fhr\Ipiccolo\Model\ResourceModel\VismaData::class
        );
    }
}
