<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Model;

use Fhr\Ipiccolo\Api\Data;
use Fhr\Ipiccolo\Api\Data\VismaDataInterface;
use Fhr\Ipiccolo\Api\Data\VismaDataInterfaceFactory;
use Fhr\Ipiccolo\Api\Data\VismaDataSearchResultsInterfaceFactory;
use Fhr\Ipiccolo\Api\VismaDataRepositoryInterface;
use Fhr\Ipiccolo\Model\ResourceModel\VismaData as ResourceVismaData;
use Fhr\Ipiccolo\Model\ResourceModel\VismaData\CollectionFactory as VismaDataCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class VismaDataRepository implements VismaDataRepositoryInterface
{

    /**
     * @var ResourceVismaData
     */
    protected $resource;

    /**
     * @var VismaDataInterfaceFactory
     */
    protected $vismaDataFactory;

    /**
     * @var VismaDataCollectionFactory
     */
    protected $vismaDataCollectionFactory;

    /**
     * @var VismaData
     */
    protected $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;


    /**
     * @param ResourceVismaData $resource
     * @param VismaDataInterfaceFactory $vismaDataFactory
     * @param VismaDataCollectionFactory $vismaDataCollectionFactory
     * @param VismaDataSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceVismaData $resource,
        VismaDataInterfaceFactory $vismaDataFactory,
        VismaDataCollectionFactory $vismaDataCollectionFactory,
        VismaDataSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->vismaDataFactory = $vismaDataFactory;
        $this->vismaDataCollectionFactory = $vismaDataCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(VismaDataInterface $vismaData)
    {
        try {
            $this->resource->save($vismaData);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the vismaData: %1',
                $exception->getMessage()
            ));
        }
        return $vismaData;
    }

    /**
     * @inheritDoc
     */
    public function get($vismaDataId)
    {
        $vismaData = $this->vismaDataFactory->create();
        $this->resource->load($vismaData, $vismaDataId);
        if (!$vismaData->getId()) {
            throw new NoSuchEntityException(__('VismaData with id "%1" does not exist.', $vismaDataId));
        }
        return $vismaData;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->vismaDataCollectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(VismaDataInterface $vismaData)
    {
        try {
            $vismaDataModel = $this->vismaDataFactory->create();
            $this->resource->load($vismaDataModel, $vismaData->getVismadataId());
            $this->resource->delete($vismaDataModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the VismaData: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($vismaDataId)
    {
        return $this->delete($this->get($vismaDataId));
    }

    /**
     * @inheritDoc
     */
    public function getByOrderId($orderId): Data\VismaDataInterface
    {
        $vismaDataModel = $this->vismaDataFactory->create();
        $this->resource->load($vismaDataModel, $orderId, 'order_id');
        return $vismaDataModel;
    }
}
