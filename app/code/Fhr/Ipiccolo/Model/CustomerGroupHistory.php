<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\Ipiccolo\Model;

use Fhr\Ipiccolo\Api\Data\CustomerGroupHistoryExtensionInterface;
use Fhr\Ipiccolo\Api\Data\CustomerGroupHistoryInterface;
use Magento\Framework\Model\AbstractModel;

class CustomerGroupHistory extends AbstractModel implements CustomerGroupHistoryInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Fhr\Ipiccolo\Model\ResourceModel\CustomerGroupHistory::class);
    }

    /**
     * @inheritDoc
     */
    public function getCustomergrouphistoryId()
    {
        return $this->getData(self::CUSTOMERGROUPHISTORY_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCustomergrouphistoryId($customergrouphistoryId)
    {
        return $this->setData(self::CUSTOMERGROUPHISTORY_ID, $customergrouphistoryId);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerId()
    {
        return $this->getData(self::CUSTOMER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerId($customerId)
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }

    /**
     * @inheridoc
     */
    public function getFromGroupId()
    {
        return $this->_get(self::FROM_GROUP_ID);
    }

    /**
     * @inheridoc
     */
    public function setFromGroupId($fromGroupId)
    {
        return $this->setData(self::FROM_GROUP_ID, $fromGroupId);
    }

    /**
     * @inheridoc
     */
    public function getToGroupId()
    {
        return $this->_get(self::TO_GROUP_ID);
    }

    /**
     * @inheridoc
     */
    public function setToGroupId($toGroupId)
    {
        return $this->setData(self::TO_GROUP_ID, $toGroupId);
    }

    /**
     * @inheridoc
     */
    public function getCreatedAt()
    {
        return $this->_get(self::CREATED_AT);
    }

    /**
     * @inheridoc
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheridoc
     */
    public function getUpdatedAt()
    {
        return $this->_get(self::UPDATED_AT);
    }

    /**
     * @inheridoc
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @inheridoc
     */
    public function getDescription()
    {
        return $this->_get(self::DESCRIPTION);
    }

    /**
     * @inheridoc
     */
    public function setDescription($description)
    {
        return $this->setData(self::DESCRIPTION, $description);
    }
}
