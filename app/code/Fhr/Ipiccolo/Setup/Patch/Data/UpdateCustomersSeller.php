<?php

namespace Fhr\Ipiccolo\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Eav\Model\Entity\Attribute\SetFactory;
use Magento\Customer\Model\Customer;

class UpdateCustomersSeller implements DataPatchInterface
{
    const ATTR_FHR_ORDERSELLERSMANAGER_USERID = 'fhr_ordersellersmanager_userid';
    const TABLE_CUSTOMER_ENTITY_INT = 'customer_entity_int';
    const FHR_WEBSHOP = 0;

    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;
    /**
     * @var CustomerSetupFactory
     */
    private $customerSetupFactory;
    /**
     * @var SetFactory
     */
    private $attributeSetFactory;

    /**
     * Constructor
     *
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param CustomerSetupFactory $customerSetupFactory
     * @param SetFactory $attributeSetFactory
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        CustomerSetupFactory     $customerSetupFactory,
        SetFactory               $attributeSetFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->customerSetupFactory = $customerSetupFactory;
        $this->attributeSetFactory = $attributeSetFactory;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, self::ATTR_FHR_ORDERSELLERSMANAGER_USERID);
        $attributeId = $attribute->getId();
        $table = $this->moduleDataSetup->getTable(self::TABLE_CUSTOMER_ENTITY_INT);
        // Move Linus customers to Webshop
        foreach ($this->getLinusCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Andreas customers to Webshop
        foreach ($this->getAndreasCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Iteb customers to Webshop
        foreach ($this->getItebCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Alfred customers to Webshop
        foreach ($this->getAlfredCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move IpiccoloEM to Webshop
        foreach ($this->getIpiccoloEMCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Braulio to Webshop
        foreach ($this->getBraulioCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Lilith to Webshop
        foreach ($this->getLilithCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Setareh to Webshop
        foreach ($this->getSetarehCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Jarmo to Webshop
        foreach ($this->getJarmoCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
        // Move Other to Webshop
        foreach ($this->getOtherCustomerList() as $entityId) {
            $this->moduleDataSetup->getConnection()->update(
                $table,
                ['value' => self::FHR_WEBSHOP],
                ['attribute_id = ?' => $attributeId, 'entity_id = ?' => $entityId]
            );
        }
    }

    public function getLinusCustomerList()
    {
        return [
            2795, // Amaar Al-Sundook <EMAIL>
            3304, // Rody <NAME_EMAIL>
            2096, // <NAME_EMAIL>
            1713, // <NAME_EMAIL>
            2736, // S.A. Faizi <EMAIL>
            2909, // <NAME_EMAIL>
            2305, // <NAME_EMAIL>
            2139, // <NAME_EMAIL>
            2031, // <NAME_EMAIL>
            3593, // Mohamad <NAME_EMAIL>
            1717, // <NAME_EMAIL>
            2583, // <NAME_EMAIL>
            1951, // <NAME_EMAIL>
            3143, // <NAME_EMAIL>
            3475, // Juan Jose <NAME_EMAIL>
            3546, // Anton Rütman <EMAIL>
            3118, // <NAME_EMAIL>
            3499, // <NAME_EMAIL>
            2216, // <NAME_EMAIL>
            2214, // <NAME_EMAIL>
            3573, // <NAME_EMAIL>
            2524, // <NAME_EMAIL>
            3550, // <NAME_EMAIL>
            3237, // <NAME_EMAIL>
            1947, // Atli <NAME_EMAIL>
            2912, // <NAME_EMAIL>
            2094, // <NAME_EMAIL>
            2633, // <NAME_EMAIL>
            3330, // Spela <NAME_EMAIL>
            2554, // <NAME_EMAIL>
            2463, // Ivan <NAME_EMAIL>
            3444, // <NAME_EMAIL>
            3480, // <NAME_EMAIL>
            3061, // <NAME_EMAIL>
            3234, // <NAME_EMAIL>
            2850, // <NAME_EMAIL>
            3284, // <NAME_EMAIL>
            3409, // <NAME_EMAIL>
            3591, // <NAME_EMAIL>
            3334, // Kai <NAME_EMAIL>
            2027, // <NAME_EMAIL>
            2814, // <NAME_EMAIL>
            3497, // <NAME_EMAIL>
            3557, // lindon  <NAME_EMAIL>
            2307, // <NAME_EMAIL>
            1293, // Anna Hällström <EMAIL>
            1366, // <NAME_EMAIL>
            3044, // <NAME_EMAIL>
            2929, // <NAME_EMAIL>
            876, // <NAME_EMAIL>
            1748, // <NAME_EMAIL>
            1328, // <NAME_EMAIL>
            2845, // <NAME_EMAIL>
            3121, // <NAME_EMAIL>
            1436, // <NAME_EMAIL>
            3693, // <NAME_EMAIL>
            671, // Linus Hilmgård <EMAIL>
            3105, // <NAME_EMAIL>
            1322, // <NAME_EMAIL>
            387, // Trestads Mobil <NAME_EMAIL>
            3731, // <NAME_EMAIL>
            10, // <NAME_EMAIL>
            3732, // <NAME_EMAIL>
            2682, // <NAME_EMAIL>
            1184, // <NAME_EMAIL>
            3746, // <NAME_EMAIL>
            130, // Kari Heikkilä <EMAIL>
            3768, // <NAME_EMAIL>
            625, // <NAME_EMAIL>
            137, // <NAME_EMAIL>
            474, // <NAME_EMAIL>
            3096, // Juho Särkiö <EMAIL>
            3799, // Pharma west as <NAME_EMAIL>
            247, // <NAME_EMAIL>
            663, // <NAME_EMAIL>
            3087, // Jonny Bramstång <EMAIL>
            3221, // Petro Härfstrand <EMAIL>
            506, // <NAME_EMAIL>
            434, // <NAME_EMAIL>
            1303, // <NAME_EMAIL>
            784, // <NAME_EMAIL>
            3823, // Serhat Günay <EMAIL>
            3060, // <NAME_EMAIL>
            3832, // <NAME_EMAIL>
            3837, // <NAME_EMAIL>
            3208, // <NAME_EMAIL>
            3886, // <NAME_EMAIL>
            3890, // <NAME_EMAIL>
            3894, // <NAME_EMAIL>
            3898, // <NAME_EMAIL>
            3907, // <NAME_EMAIL>
            3914, // <NAME_EMAIL>
            3937, // <NAME_EMAIL>
            3945, // <NAME_EMAIL>
            3963, // <NAME_EMAIL>
            3964, // <NAME_EMAIL>
            3976, // Joel Olefalk-Zand <EMAIL>
            3983, // <NAME_EMAIL>
            1514, // <NAME_EMAIL>
            4003, // Sebastiaan <NAME_EMAIL>
            4006, // <NAME_EMAIL>
            4014, // <NAME_EMAIL>
            2150, // <NAME_EMAIL>
            4048, // <NAME_EMAIL>
            3892, // <NAME_EMAIL>
            4096, // <NAME_EMAIL>
            3416, // <NAME_EMAIL>
            4115, // <NAME_EMAIL>
            4133, // <NAME_EMAIL>
            3494, // <NAME_EMAIL>
            4134, // <NAME_EMAIL>
            4164, // <NAME_EMAIL>
            4194, // <NAME_EMAIL>
            4228, // <NAME_EMAIL>
            4262, // <NAME_EMAIL>
            4269, // Daniel <NAME_EMAIL>
            4365, // <NAME_EMAIL>
            4370, // <NAME_EMAIL>
            4423, // <NAME_EMAIL>
            2202, // <NAME_EMAIL>
            1453, // <NAME_EMAIL>
            4516, // Daniel <NAME_EMAIL>
            4528, // <NAME_EMAIL>
            2389, // <NAME_EMAIL>
            4616, // <NAME_EMAIL>
            4639, // Boulent <NAME_EMAIL>
            4706, // <NAME_EMAIL>
            4788, // <NAME_EMAIL>
            4790, // <NAME_EMAIL>
            4806, // <NAME_EMAIL>
            4837, // <NAME_EMAIL>
            4882, // <NAME_EMAIL>
            4924, // <NAME_EMAIL>
            4944, // Luis <NAME_EMAIL>
            4953, // <NAME_EMAIL>
            4963, // Marwan <NAME_EMAIL>
            4969, // Nicolas <NAME_EMAIL>
            5027, // <NAME_EMAIL>
            5028, // <NAME_EMAIL>
            5296, // <NAME_EMAIL>
            5357, // <NAME_EMAIL>
            5358, // <NAME_EMAIL>
            5359, // <NAME_EMAIL>
            5589, // <NAME_EMAIL>
            5662, // <NAME_EMAIL>
            5775, // <NAME_EMAIL>
            5799, // Mikael König <EMAIL>
            5818, // <NAME_EMAIL>
            5994, // <NAME_EMAIL>
        ];
    }

    public function getAndreasCustomerList()
    {
        return [
            3289, // <NAME_EMAIL>
            2226, // <NAME_EMAIL>
            1027, // <NAME_EMAIL>
            1022, // Tor <NAME_EMAIL>
            950, // <NAME_EMAIL>
            3315, // <NAME_EMAIL>
            3781, // <NAME_EMAIL>
            3708, // <NAME_EMAIL>
            3791, // <NAME_EMAIL>
            3792, // Mays Al-Waeli <EMAIL>
            848, // <NAME_EMAIL>
            2164, // <NAME_EMAIL>
            3953, // Tomáš Getty <EMAIL>
            1878, // David Vlček <EMAIL>
            4103, // <NAME_EMAIL>
            4119, // <NAME_EMAIL>
            4138, // <NAME_EMAIL>
            4139, // <NAME_EMAIL>
            4602, // <NAME_EMAIL>
            100, // <NAME_EMAIL>
            618, // <NAME_EMAIL>
            2019, // <NAME_EMAIL>
            4842, // <NAME_EMAIL>
            4868, // <NAME_EMAIL>
            5034, // <NAME_EMAIL>
            5791, // <NAME_EMAIL>
        ];
    }

    public function getItebCustomerList()
    {
        return [
            3552, // <NAME_EMAIL>
            3454, // <NAME_EMAIL>
            2622, // <NAME_EMAIL>
            3381, // <NAME_EMAIL>
            3495, // Daniele <NAME_EMAIL>
            2377, // <NAME_EMAIL>
            2672, // <NAME_EMAIL>
            2054, // <NAME_EMAIL>
            3213, // <NAME_EMAIL>
            1916, // <NAME_EMAIL>
            2059, // <NAME_EMAIL>
            1954, // <NAME_EMAIL>
            3129, // <NAME_EMAIL>
            1370, // <NAME_EMAIL>
            2444, // El houssaine <NAME_EMAIL>
            1390, // <NAME_EMAIL>
            2878, // <NAME_EMAIL>
            3697, // <NAME_EMAIL>
            2552, // <NAME_EMAIL>
            3745, // <NAME_EMAIL>
            1875, // <NAME_EMAIL>
            3778, // <NAME_EMAIL>
            1360, // <NAME_EMAIL>
            3812, // <NAME_EMAIL>
            3838, // <NAME_EMAIL>
            1414, // <NAME_EMAIL>
            3934, // <NAME_EMAIL>
            3948, // DI GADALETA <NAME_EMAIL>
            4011, // <NAME_EMAIL>
            4029, // <NAME_EMAIL>
            4034, // <NAME_EMAIL>
            4044, // <NAME_EMAIL>
            4053, // <NAME_EMAIL>
            4063, // <NAME_EMAIL>
            3913, // <NAME_EMAIL>
            3932, // <NAME_EMAIL>
            3786, // <NAME_EMAIL>
            3878, // <NAME_EMAIL>
            2607, // <NAME_EMAIL>
            1446, // ouzouigh <NAME_EMAIL>
            3840, // <NAME_EMAIL>
            3931, // <NAME_EMAIL>
            3895, // <NAME_EMAIL>
            3999, // <NAME_EMAIL>
            3992, // <NAME_EMAIL>
            3674, // Giuseppe Luca <NAME_EMAIL>
            3905, // <NAME_EMAIL>
            3943, // <NAME_EMAIL>
            3631, // Mario <NAME_EMAIL>
            3846, // <NAME_EMAIL>
            3930, // Christian <NAME_EMAIL>
            3912, // <NAME_EMAIL>
            3856, // <NAME_EMAIL>
            3873, // <NAME_EMAIL>
            3871, // <NAME_EMAIL>
            3860, // <NAME_EMAIL>
            3854, // <NAME_EMAIL>
            3833, // <NAME_EMAIL>
            2188, // <NAME_EMAIL>
            3803, // <NAME_EMAIL>
            3694, // <NAME_EMAIL>
            3802, // <NAME_EMAIL>
            3702, // <NAME_EMAIL>
            3716, // <NAME_EMAIL>
            3714, // <NAME_EMAIL>
            3699, // <NAME_EMAIL>
            3614, // <NAME_EMAIL>
            3612, // <NAME_EMAIL>
            4065, // <NAME_EMAIL>
            4069, // <NAME_EMAIL>
            4075, // <NAME_EMAIL>
            1273, // <NAME_EMAIL>
            4089, // wellington Rangel <NAME_EMAIL>
            4091, // alessandro <NAME_EMAIL>
            4088, // Salvatore <NAME_EMAIL>
            2440, // <NAME_EMAIL>
            4098, // <NAME_EMAIL>
            4104, // Philippe De Cintré <EMAIL>
            4110, // <NAME_EMAIL>
            4131, // <NAME_EMAIL>
            2823, // <NAME_EMAIL>
            4137, // <NAME_EMAIL>
            3348, // <NAME_EMAIL>
            1989, // ben <NAME_EMAIL>
            1871, // <NAME_EMAIL>
            4222, // <NAME_EMAIL>
            4237, // <NAME_EMAIL>
            4304, // <NAME_EMAIL>
            4310, // <NAME_EMAIL>
            4312, // <NAME_EMAIL>
            4341, // <NAME_EMAIL>
            4397, // <NAME_EMAIL>
            4481, // <NAME_EMAIL>
            4470, // <NAME_EMAIL>
            4489, // <NAME_EMAIL>
            4484, // <NAME_EMAIL>
            4495, // <NAME_EMAIL>
            4497, // <NAME_EMAIL>
            4506, // <NAME_EMAIL>
            4505, // <NAME_EMAIL>
            4522, // <NAME_EMAIL>
            4518, // <NAME_EMAIL>
            4526, // <NAME_EMAIL>
            4499, // <NAME_EMAIL>
            4537, // <NAME_EMAIL>
            4547, // <NAME_EMAIL>
            4410, // <NAME_EMAIL>
            4580, // Gianfranco <NAME_EMAIL>
            4576, // <NAME_EMAIL>
            4594, // <NAME_EMAIL>
            1357, // Quentin Préaux <EMAIL>
            1726, // <NAME_EMAIL>
            1721, // <NAME_EMAIL>
            1802, // BURCHI jean-luc <EMAIL>
            1766, // point <NAME_EMAIL>
            1917, // <NAME_EMAIL>
            2231, // <NAME_EMAIL>
            2469, // <NAME_EMAIL>
            1990, // <NAME_EMAIL>
            2748, // <NAME_EMAIL>
            3397, // <NAME_EMAIL>
            1004, // <NAME_EMAIL>
            1062, // <NAME_EMAIL>
            1354, // <NAME_EMAIL>
            1355, // pieroni charles-eric <EMAIL>
            1356, // <NAME_EMAIL>
            1358, // <NAME_EMAIL>
            1359, // <NAME_EMAIL>
            1376, // <NAME_EMAIL>
            1382, // <NAME_EMAIL>
            1385, // <NAME_EMAIL>
            1388, // <NAME_EMAIL>
            1401, // <NAME_EMAIL>
            1410, // <NAME_EMAIL>
            1416, // TRERIEUX Sébastien <EMAIL>
            1432, // <NAME_EMAIL>
            1455, // <NAME_EMAIL>
            1466, // <NAME_EMAIL>
            1540, // <NAME_EMAIL>
            1548, // Kazi <NAME_EMAIL>
            1583, // <NAME_EMAIL>
            1595, // <NAME_EMAIL>
            1631, // <NAME_EMAIL>
            1637, // <NAME_EMAIL>
            1683, // <NAME_EMAIL>
            1711, // <NAME_EMAIL>
            1723, // <NAME_EMAIL>
            1728, // <NAME_EMAIL>
            1742, // J-<NAME_EMAIL>
            1795, // <NAME_EMAIL>
            1823, // <NAME_EMAIL>
            1825, // <NAME_EMAIL>
            1845, // <NAME_EMAIL>
            1846, // <NAME_EMAIL>
            1855, // <NAME_EMAIL>
            1859, // <NAME_EMAIL>
            1872, // <NAME_EMAIL>
            1892, // <NAME_EMAIL>
            1913, // <NAME_EMAIL>
            1927, // <NAME_EMAIL>
            1946, // <NAME_EMAIL>
            1952, // <NAME_EMAIL>
            1955, // <NAME_EMAIL>
            1966, // <NAME_EMAIL>
            1970, // Cavarec Yvan  <EMAIL>
            1984, // <NAME_EMAIL>
            1997, // <NAME_EMAIL>
            2003, // Georges Gabro PSM EVRY/SAVE <EMAIL>
            2013, // <NAME_EMAIL>
            2039, // Sté<NAME_EMAIL>
            2042, // <NAME_EMAIL>
            2045, // <NAME_EMAIL>
            2122, // <NAME_EMAIL>
            2141, // <NAME_EMAIL>
            2142, // ismail <NAME_EMAIL>
            2154, // Amenta Sébastien <EMAIL>
            2169, // <NAME_EMAIL>
            2190, // <NAME_EMAIL>
            2191, // <NAME_EMAIL>
            2192, // José Vieira <EMAIL>
            2205, // <NAME_EMAIL>
            2218, // <NAME_EMAIL>
            2222, // <NAME_EMAIL>
            2224, // Nasser  MOUDNIB  <EMAIL>
            2250, // <NAME_EMAIL>
            2282, // Valé<NAME_EMAIL>
            2333, // MON REPARATEUR iGAME Mr <NAME_EMAIL>
            2352, // <NAME_EMAIL>
            2368, // <NAME_EMAIL>
            2375, // <NAME_EMAIL>
            2379, // <NAME_EMAIL>
            2392, // <NAME_EMAIL>
            2397, // <NAME_EMAIL>
            2448, // <NAME_EMAIL>
            2451, // <NAME_EMAIL>
            2540, // <NAME_EMAIL>
            2541, // <NAME_EMAIL>
            2553, // <NAME_EMAIL>
            2590, // <NAME_EMAIL>
            2602, // <NAME_EMAIL>
            2618, // <NAME_EMAIL>
            2628, // Sarl bemobile  Pierre Derycke  <EMAIL>
            2629, // <NAME_EMAIL>
            2652, // <NAME_EMAIL>
            2656, // <NAME_EMAIL>
            2676, // <NAME_EMAIL>
            2692, // jean-<NAME_EMAIL>
            2805, // <NAME_EMAIL>
            2822, // <NAME_EMAIL>
            2874, // <NAME_EMAIL>
            2876, // <NAME_EMAIL>
            2877, // <NAME_EMAIL>
            2902, // <NAME_EMAIL>
            2903, // <NAME_EMAIL>
            2926, // <NAME_EMAIL>
            2949, // <NAME_EMAIL>
            2973, // <NAME_EMAIL>
            2978, // <NAME_EMAIL>
            2991, // Sté<NAME_EMAIL>
            3010, // <NAME_EMAIL>
            3019, // <NAME_EMAIL>
            3024, // <NAME_EMAIL>
            3031, // Rebecca  LABBE <EMAIL>
            3047, // <NAME_EMAIL>
            3078, // <NAME_EMAIL>
            3093, // <NAME_EMAIL>
            3097, // <NAME_EMAIL>
            3119, // <NAME_EMAIL>
            3188, // <NAME_EMAIL>
            3190, // <NAME_EMAIL>
            3227, // <NAME_EMAIL>
            3280, // <NAME_EMAIL>
            3331, // <NAME_EMAIL>
            3349, // <NAME_EMAIL>
            3352, // <NAME_EMAIL>
            3433, // EL <NAME_EMAIL>
            3477, // <NAME_EMAIL>
            3537, // <NAME_EMAIL>
            3555, // <NAME_EMAIL>
            3559, // <NAME_EMAIL>
            3560, // <NAME_EMAIL>
            3561, // MADDELEIN STEPHANE <NAME_EMAIL>
            3965, // <NAME_EMAIL>
            4329, // <NAME_EMAIL>
            4368, // <NAME_EMAIL>
            4400, // <NAME_EMAIL>
            4630, // <NAME_EMAIL>
            4652, // <NAME_EMAIL>
            4660, // <NAME_EMAIL>
            4668, // <NAME_EMAIL>
            4669, // <NAME_EMAIL>
            4670, // <NAME_EMAIL>
            4675, // uJ875<NAME_EMAIL>
            4679, // <NAME_EMAIL>
            4705, // <NAME_EMAIL>
            4739, // MUSTAPHA <NAME_EMAIL>
            4742, // el <NAME_EMAIL>
            4744, // <NAME_EMAIL>
            4760, // <NAME_EMAIL>
            4800, // <NAME_EMAIL>
            4822, // <NAME_EMAIL>
            4861, // <NAME_EMAIL>
            4892, // <NAME_EMAIL>
            4910, // wA vHR1qjS7e <EMAIL>
            4937, // <NAME_EMAIL>
            4960, // <NAME_EMAIL>
            4964, // <NAME_EMAIL>
            4974, // <NAME_EMAIL>
            4975, // b2zi3<NAME_EMAIL>
            4976, // <NAME_EMAIL>
            4980, // <NAME_EMAIL>
            4982, // Sebastiano <NAME_EMAIL>
            4993, // <NAME_EMAIL>
            4999, // <NAME_EMAIL>
            5013, // <NAME_EMAIL>
            5056, // <NAME_EMAIL>
            5064, // <NAME_EMAIL>
            5065, // <NAME_EMAIL>
            5070, // <NAME_EMAIL>
            5071, // <NAME_EMAIL>
            5078, // <NAME_EMAIL>
            5105, // <NAME_EMAIL>
            5109, // <NAME_EMAIL>
            5137, // <NAME_EMAIL>
            5151, // <NAME_EMAIL>
            5163, // bisDWe a1lanH <EMAIL>
            5223, // <NAME_EMAIL>
            5227, // <NAME_EMAIL>
            5228, // <NAME_EMAIL>
            5241, // LUCIANO <NAME_EMAIL>
            5244, // Adil <NAME_EMAIL>
            5260, // <NAME_EMAIL>
            5261, // <NAME_EMAIL>
            5274, // <NAME_EMAIL>
            5275, // <NAME_EMAIL>
            5277, // <NAME_EMAIL>
            5278, // <NAME_EMAIL>
            5280, // <NAME_EMAIL>
            5281, // <NAME_EMAIL>
            5286, // <NAME_EMAIL>
            5292, // ENRICO <NAME_EMAIL>
            5297, // Jmo Mobiles <NAME_EMAIL>
            5301, // <NAME_EMAIL>
            5302, // <NAME_EMAIL>
            5306, // <NAME_EMAIL>
            5307, // <NAME_EMAIL>
            5309, // <NAME_EMAIL>
            5312, // <NAME_EMAIL>
            5315, // <NAME_EMAIL>
            5320, // <NAME_EMAIL>
            5329, // <NAME_EMAIL>
            5333, // <NAME_EMAIL>
            5344, // <NAME_EMAIL>
            5360, // <NAME_EMAIL>
            5362, // <NAME_EMAIL>
            5394, // <NAME_EMAIL>
            5406, // <NAME_EMAIL>
            5431, // <NAME_EMAIL>
            5435, // <NAME_EMAIL>
            5444, // <NAME_EMAIL>
            5445, // <NAME_EMAIL>
            5471, // <NAME_EMAIL>
            5491, // ben <NAME_EMAIL>
            5499, // <NAME_EMAIL>
            5533, // atlascom <NAME_EMAIL>
            5557, // <NAME_EMAIL>
            5563, // <NAME_EMAIL>
            5565, // Vincenzo <NAME_EMAIL>
            5566, // <NAME_EMAIL>
            5567, // <NAME_EMAIL>
            5568, // <NAME_EMAIL>
            5582, // <NAME_EMAIL>
            5583, // <NAME_EMAIL>
            5592, // <NAME_EMAIL>
            5594, // <NAME_EMAIL>
            5595, // <NAME_EMAIL>
            5597, // <NAME_EMAIL>
            5598, // <NAME_EMAIL>
            5599, // <NAME_EMAIL>
            5602, // <NAME_EMAIL>
            5603, // <NAME_EMAIL>
            5605, // <NAME_EMAIL>
            5612, // <NAME_EMAIL>
            5617, // <NAME_EMAIL>
            5621, // <NAME_EMAIL>
            5622, // <NAME_EMAIL>
            5623, // <NAME_EMAIL>
            5625, // <NAME_EMAIL>
            5626, // <NAME_EMAIL>
            5628, // <NAME_EMAIL>
            5629, // <NAME_EMAIL>
            5630, // <NAME_EMAIL>
            5636, // <NAME_EMAIL>
            5637, // <NAME_EMAIL>
            5639, // <NAME_EMAIL>
            5642, // <NAME_EMAIL>
            5644, // <NAME_EMAIL>
            5645, // <NAME_EMAIL>
            5653, // <NAME_EMAIL>
            5654, // <NAME_EMAIL>
            5655, // <NAME_EMAIL>
            5660, // mohamed <NAME_EMAIL>
            5664, // <NAME_EMAIL>
            5666, // <NAME_EMAIL>
            5668, // <NAME_EMAIL>
            5670, // <NAME_EMAIL>
            5671, // <NAME_EMAIL>
            5675, // <NAME_EMAIL>
            5678, // <NAME_EMAIL>
            5679, // <NAME_EMAIL>
            5681, // <NAME_EMAIL>
            5682, // Zeroual Rémy <EMAIL>
            5684, // <NAME_EMAIL>
            5686, // <NAME_EMAIL>
            5690, // <NAME_EMAIL>
            5691, // <NAME_EMAIL>
            5692, // <NAME_EMAIL>
            5694, // <NAME_EMAIL>
            5698, // <NAME_EMAIL>
        ];
    }

    public function getAlfredCustomerList()
    {
        return [
            5896, // Alfred <NAME_EMAIL>
        ];
    }

    public function getIpiccoloEMCustomerList()
    {
        return [
            4636, // <NAME_EMAIL>
        ];
    }

    public function getBraulioCustomerList()
    {
        return [
            10100008, // <NAME_EMAIL>
        ];
    }

    public function getLilithCustomerList()
    {
        return [
            4019, // <NAME_EMAIL>
            2941, // <NAME_EMAIL>
            2778, // <NAME_EMAIL>
            4043, // <NAME_EMAIL>
            4049, // <NAME_EMAIL>
            3286, // <NAME_EMAIL>
            4058, // <NAME_EMAIL>
            3592, // Ali <NAME_EMAIL>
            2038, // Liam O'Connor <EMAIL>
            3683, // <NAME_EMAIL>
            3761, // <NAME_EMAIL>
            906, // <NAME_EMAIL>
            1048, // <NAME_EMAIL>
            483, // <NAME_EMAIL>
            3058, // <NAME_EMAIL>
            875, // <NAME_EMAIL>
            2625, // <NAME_EMAIL>
            3464, // <NAME_EMAIL>
            3527, // <NAME_EMAIL>
        ];
    }

    public function getSetarehCustomerList()
    {
        return [
            4425, // <NAME_EMAIL>
            4572, // <NAME_EMAIL>
        ];
    }

    public function getJarmoCustomerList()
    {
        return [
            5529, // <NAME_EMAIL>
            5531, // <NAME_EMAIL>
        ];
    }

    public function getOtherCustomerList()
    {
        return [
            1026, // Rune  Steinsvik  <EMAIL>
            1147, // Jo <NAME_EMAIL>
            768, // <NAME_EMAIL>
            611, // <NAME_EMAIL>
        ];
    }
}
