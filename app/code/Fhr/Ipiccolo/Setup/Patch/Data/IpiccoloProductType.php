<?php

namespace Fhr\Ipiccolo\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetup;
use <PERSON>gento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchInterface;

class IpiccoloProductType implements DataPatchInterface
{
    const DEFAULT_ATTRIBUTE_SET = 'Migration_Default';
    const ATTRIBUTE_CODE = 'ipiccolo_product_type';
    const ATTRIBUTE_LABEL = 'Ipiccolo ProductType';

    /** @var ModuleDataSetupInterface */
    private $moduleDataSetup;

    /** @var EavSetupFactory */
    private $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            self::ATTRIBUTE_CODE,
            [
                'type' => 'int',
                'backend' => '',
                'frontend' => '',
                'label' => self::ATTRIBUTE_LABEL,
                'input' => 'select',
                'class' => '',
                'source' => \Fhr\Ipiccolo\Model\Product\Attribute\Source\IpiccoloProductType::class,
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL,
                'visible' => true,
                'required' => false,
                'user_defined' => true,
                'default' => 0,
                'searchable' => true,
                'filterable' => true,
                'comparable' => true,
                'visible_on_front' => false,
                'used_in_product_listing' => true,
                'unique' => false,
            ]
        );
        // Add attribute to attribute set
        $eavSetup->addAttributeToSet(
            \Magento\Catalog\Model\Product::ENTITY,
            self::DEFAULT_ATTRIBUTE_SET,
            'General',
            self::ATTRIBUTE_CODE,
            830
        );
        // Add Attribute to grid and filters
        $eavSetup->updateAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            self::ATTRIBUTE_CODE,
            'is_visible_in_grid',
            1
        );
        $eavSetup->updateAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            self::ATTRIBUTE_CODE,
            'is_filterable_in_grid',
            1
        );
        $eavSetup->updateAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            self::ATTRIBUTE_CODE,
            'is_searchable_in_grid',
            1
        );
        $eavSetup->updateAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            self::ATTRIBUTE_CODE,
            'is_used_in_grid',
            1
        );
    }
}
