<?php

namespace Fhr\Ipiccolo\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetup;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchInterface;

class DisableRmaStatus7 implements DataPatchInterface
{
    const RMA_STATUS_CODE_CREDIT_APPLIED = 'credit_applied';
    const TABLE = 'mst_rma_status';

    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        $table = $this->moduleDataSetup->getTable(self::TABLE);
        $this->moduleDataSetup->getConnection()->update(
            $table,
            ['is_active' => '0'],
            ['code = ?' => self::RMA_STATUS_CODE_CREDIT_APPLIED]
        );
    }
}
