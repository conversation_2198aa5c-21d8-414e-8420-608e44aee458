<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\RestrictAddToCart\Plugin\Backend\Magento\Quote\Model\Quote\Item;

use Fhr\RestrictAddToCart\Model\Common;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Message\ManagerInterface;
use MageWorx\OrderEditor\Api\QuoteRepositoryInterface;

class AbstractItem
{

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;
    /**
     * @var Common
     */
    private $common;
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;
    /**
     * @var ManagerInterface
     */
    private $messageManager;
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    /**
     * @param ResourceConnection $resourceConnection
     * @param ManagerInterface $messageManager
     * @param CustomerRepositoryInterface $customerRepository
     * @param ProductRepositoryInterface $productRepository
     * @param Common $common
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ResourceConnection          $resourceConnection,
        ManagerInterface            $messageManager,
        CustomerRepositoryInterface $customerRepository,
        ProductRepositoryInterface  $productRepository,
        Common                      $common,
        ScopeConfigInterface        $scopeConfig
    ) {
        $this->resourceConnection = $resourceConnection;
        $this->messageManager = $messageManager;
        $this->customerRepository = $customerRepository;
        $this->productRepository = $productRepository;
        $this->common = $common;
        $this->scopeConfig = $scopeConfig;
    }

    public function afterCheckData(
        \Magento\Quote\Model\Quote\Item\AbstractItem $subject,
        $result
    ) {
        if (!$this->scopeConfig->getValue(Common::CONFIG_ENABLED_ADMINHTML)) {
            return $result;
        }

        if ($this->check($subject)) {
            $subject->setHasError(true);
            $subject->setMessage(__(Common::PHRASE_ABLE_TO_DELIVER_BATTERIES));
            $subject->getQuote()->setHasError(true)->addMessage(__(Common::PHRASE_ABLE_TO_DELIVER_BATTERIES));
        }

        return $result;
    }

    /**
     * @param \Magento\Quote\Model\Quote\Item\AbstractItem $subject
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function check(\Magento\Quote\Model\Quote\Item\AbstractItem $subject): bool
    {
        $productId = $subject->getProduct()->getId();
        $customerId = $this->getCustomerIdByQuoteId((int)$subject->getQuoteId());
        $product = $this->productRepository->getById($productId);
        $customer = $this->customerRepository->getById($customerId);
        return $this->common->isBatteryRestrictAddToCart($product, $customer);
    }

    /**
     * @param int $getQuoteId
     * @return string
     */
    private function getCustomerIdByQuoteId(int $getQuoteId): string
    {
        $connection = $this->resourceConnection->getConnection();
        $table = $connection->getTableName('quote');
        $select = $connection->select()
            ->from($table, 'customer_id')
            ->where('entity_id = ?', $getQuoteId);
        return $connection->fetchOne($select);
    }
}
