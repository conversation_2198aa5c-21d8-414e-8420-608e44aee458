<?php

namespace Fhr\RestrictAddToCart\Model;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Customer\Api\Data\CustomerInterface;

class Common
{
    const PHRASE_ABLE_TO_DELIVER_BATTERIES = 'Due to freight restrictions we can not ship batteries to your location – please contact customer service for more information';
    const CONFIG_ENABLED_FRONTEND = 'restrictaddtocart/general/enabled_frontend';
    const CONFIG_ENABLED_ADMINHTML = 'restrictaddtocart/general/enabled_adminhtml';

    /**
     * @param ProductInterface $product
     * @return boolean
     */
    public function isBattery(ProductInterface $product): bool
    {
        if ($product->getCustomAttribute(\Fhr\Ipiccolo\Setup\Patch\Data\IpiccoloBattery::ATTRIBUTE_CODE)) {
            return (bool)$product->getCustomAttribute(\Fhr\Ipiccolo\Setup\Patch\Data\IpiccoloBattery::ATTRIBUTE_CODE)->getValue();
        }
        return false;
    }

    /**
     * @param CustomerInterface $customer
     * @return bool
     */
    public function isCustomerRestrictBulkBatteries(CustomerInterface $customer): bool
    {
        if ($customer->getCustomAttribute(\Fhr\RestrictAddToCart\Setup\Patch\Data\RestrictBulkBatteries::CODE)) {
            return (bool)$customer->getCustomAttribute(\Fhr\RestrictAddToCart\Setup\Patch\Data\RestrictBulkBatteries::CODE)->getValue();
        }
        return false;
    }

    /**
     * @param ProductInterface $product
     * @param CustomerInterface $customer
     * @return bool
     */
    public function isBatteryRestrictAddToCart(ProductInterface $product, CustomerInterface $customer): bool
    {
        if ($this->isBattery($product)) {
            if ($this->isCustomerRestrictBulkBatteries($customer)) {
                return true;
            }
        }
        return false;
    }
}
