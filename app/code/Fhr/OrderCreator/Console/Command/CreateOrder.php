<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\OrderCreator\Console\Command;

use Fhr\OrderCreator\Model\Common;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class CreateOrder extends Command
{

    const NAME_ARGUMENT = "name";
    const NAME_OPTION = "option";

    const ARGUMENT_CUSTOMERID = 'customerId';
    const ARGUMENT_PRODUCTSITEMDATA = 'productsItemData';
    const ARGUMENT_SHIPPINMETHODCODE = 'shippinMethodCode';
    const ARGUMENT_PAYMENTMETHOD = 'paymentMethod';
    const ARGUMENT_STATUSSTATE = 'statusState';

    /**
     * @var Common
     */
    private $common;

    /**
     * @param Common $common
     * @param string|null $name
     */
    public function __construct(
        Common $common,
        string $name = null
    ) {
        parent::__construct($name);
        $this->common = $common;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $option = $input->getOption(self::NAME_OPTION);

        $customerId = $input->getArgument(self::ARGUMENT_CUSTOMERID);
        $productsItemData = $input->getArgument(self::ARGUMENT_PRODUCTSITEMDATA);

        $shippinMethodCode = $input->getArgument(self::ARGUMENT_SHIPPINMETHODCODE);
        $paymentMethod = $input->getArgument(self::ARGUMENT_PAYMENTMETHOD);
        $statusState = $input->getArgument(self::ARGUMENT_STATUSSTATE);

        $output->writeln(
            __(
                'Data processing Customer ID: %1, Product items: %2, Shipping code: %3, Payment method: %4, '
                .'Status and State: %5',
                $customerId,
                $productsItemData,
                $shippinMethodCode,
                $paymentMethod,
                $statusState
            )
        );

        $order = $this->common->createOrder(
            $customerId,
            $productsItemData,
            $shippinMethodCode,
            $paymentMethod,
            $statusState
        );

        if (!empty($order)) {
            $output->writeln(__('Order #%1[%2] created.', $order->getIncrementId(), $order->getEntityId()));
        } else {
            $output->writeln(__('Order not created. Check system.log for more details'));
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("fhr_ordercreator:createorder");
        $this->setDescription("Create order");
        $this->setDefinition([
            new InputArgument(self::ARGUMENT_CUSTOMERID, InputArgument::REQUIRED, "Customer Id"),
            new InputArgument(self::ARGUMENT_PRODUCTSITEMDATA, InputArgument::REQUIRED, "Product item data product_id => qty [123=>1;124=>2;125=>5]"),
            new InputArgument(self::ARGUMENT_SHIPPINMETHODCODE, InputArgument::OPTIONAL, "Shiping method code. Default: ".\Fhr\OrderCreator\Model\Common::DEFAULT_SHIPPING_METHOD),
            new InputArgument(self::ARGUMENT_PAYMENTMETHOD, InputArgument::OPTIONAL, "Payment method code. Default: ".\Fhr\OrderCreator\Model\Common::DEFAULT_PAYMENT_METHOD),
            new InputArgument(self::ARGUMENT_STATUSSTATE, InputArgument::OPTIONAL, "Status and state [status=>processing;state=>processing]"),
            new InputOption(self::NAME_OPTION, "-a", InputOption::VALUE_NONE, "Option functionality")
        ]);
        parent::configure();
    }
}
