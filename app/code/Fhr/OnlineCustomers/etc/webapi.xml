<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route method="POST" url="/V1/fhr-onlinecustomers/onlinecustomers">
		<service class="Fhr\OnlineCustomers\Api\OnlineCustomersRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Fhr_OnlineCustomers::OnlineCustomers_save"/>
		</resources>
	</route>
	<route method="GET" url="/V1/fhronlinecustomers/search">
		<service class="Fhr\OnlineCustomers\Api\OnlineCustomersRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Fhr_OnlineCustomers::OnlineCustomers_view"/>
		</resources>
	</route>
	<route method="GET" url="/V1/fhronlinecustomers/:onlinecustomersId">
		<service class="Fhr\OnlineCustomers\Api\OnlineCustomersRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Fhr_OnlineCustomers::OnlineCustomers_view"/>
		</resources>
	</route>
	<route method="PUT" url="/V1/fhronlinecustomers/:onlinecustomersId">
		<service class="Fhr\OnlineCustomers\Api\OnlineCustomersRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Fhr_OnlineCustomers::OnlineCustomers_update"/>
		</resources>
	</route>
	<route method="DELETE" url="/V1/fhronlinecustomers/:onlinecustomersId">
		<service class="Fhr\OnlineCustomers\Api\OnlineCustomersRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Fhr_OnlineCustomers::OnlineCustomers_delete"/>
		</resources>
	</route>
</routes>
