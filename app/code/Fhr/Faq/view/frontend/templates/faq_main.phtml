<div class="faq-container space-y-4">
    <?php $groupedFaqs = $block->getGroupedFaqCollection(); ?>
    <?php if (!empty($groupedFaqs)): ?>
        <?php foreach ($groupedFaqs as $groupName => $faqs): ?>
            <h2 class="faq-group-title"><?php echo $groupName; ?></h2>
            <ul x-data="{ openFaq: null }" class="faq-list">
                <?php foreach ($faqs as $faq): ?>
                    <li class="faq-item border-b">
                        <h3 @click="openFaq === <?php echo $faq->getId(); ?> ? openFaq = null : openFaq = <?php echo $faq->getId(); ?>"
                            class="text-xl cursor-pointer">
                            <?php echo $faq->getTitle(); ?>
                        </h3>
                        <div x-show="openFaq === <?php echo $faq->getId(); ?>" class="faq-content mt-2 text-sm">
                            <?php echo $faq->getContent(); ?>
                        </div>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endforeach; ?>
    <?php else: ?>
        <p>No FAQs available at this time.</p>
    <?php endif; ?>
</div>
