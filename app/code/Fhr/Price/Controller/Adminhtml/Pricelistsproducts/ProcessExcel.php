<?php

namespace Fhr\Price\Controller\Adminhtml\Pricelistsproducts;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\App\ResourceConnection;
use Magento\Store\Model\StoreManagerInterface;
use Fhr\Price\Model\PricelistsRepository;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Psr\Log\LoggerInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Fhr\Base\Model\Common;

/**
 * Class ProcessExcel
 *
 * @package Fhr\Price\Controller\Adminhtml\Pricelistsproducts
 */
class ProcessExcel extends Action
{
    const ADMIN_RESOURCE = 'Fhr_Price::Pricelistsproducts_excel';

    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var File
     */
    protected $file;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var PricelistsRepository
     */
    protected $pricelistsRepository;

    /**
     * @var LoggerInterface
     */
    protected $_logger;

    /**
     * @var ProductRepositoryInterface
     */
    protected $productRepository;

    /**
     * Constructor
     *
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param Filesystem $filesystem
     * @param File $file
     * @param ResourceConnection $resourceConnection
     * @param StoreManagerInterface $storeManager
     * @param PricelistsRepository $pricelistsRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        Filesystem $filesystem,
        File $file,
        ResourceConnection $resourceConnection,
        StoreManagerInterface $storeManager,
        PricelistsRepository $pricelistsRepository,
        LoggerInterface $logger,
        ProductRepositoryInterface $productRepository
    ) {
        $this->resultJsonFactory = $resultJsonFactory;
        $this->filesystem = $filesystem;
        $this->file = $file;
        $this->resourceConnection = $resourceConnection;
        $this->storeManager = $storeManager;
        $this->pricelistsRepository = $pricelistsRepository;
        $this->_logger = $logger;
        $this->productRepository = $productRepository;
        parent::__construct($context);
    }

    /**
     * Process Excel file
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $result = $this->resultJsonFactory->create();

        try {
            if (!$this->getRequest()->isPost()) {
                throw new LocalizedException(__('Only POST method is allowed'));
            }

            $pricelistId = $this->getRequest()->getParam('pricelist_id');
            $templateType = $this->getRequest()->getParam('template_type');
            $currency = $this->getRequest()->getParam('currency');

            if (empty($pricelistId)) {
                throw new LocalizedException(__('Please select a pricelist'));
            }

            if (empty($templateType)) {
                throw new LocalizedException(__('Please select a template type'));
            }

            if (empty($currency)) {
                throw new LocalizedException(__('Please select a currency'));
            }

            // Check if file was uploaded
            if (!isset($_FILES['excel_file']) || !$_FILES['excel_file']['tmp_name']) {
                throw new LocalizedException(__('Please upload an Excel file'));
            }

            // Process the Excel file and update pricelist
            $updatedProducts = $this->processExcelData($_FILES['excel_file']['tmp_name'], $pricelistId, $templateType, $currency);

            return $result->setData([
                'success' => true,
                'message' => __('Excel file processed successfully. Pricelist discounts have been updated.'),
                'updated_products' => $updatedProducts
            ]);

        } catch (LocalizedException $e) {
            return $result->setData([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            return $result->setData([
                'success' => false,
                'message' => __('An error occurred while processing the file: %1', $e->getMessage())
            ]);
        }
    }

    /**
     * Process Excel data and update pricelist
     *
     * @param string $filePath
     * @param int $pricelistId
     * @param string $templateType
     * @param string $currency
     * @return array
     * @throws \Exception
     */
    private function processExcelData($filePath, $pricelistId, $templateType, $currency = 'SEK')
    {
        // Define template configurations with support for sheet-specific configurations
        $columnConfigs = [
            'standard' => [
                'Default' => [["B", "C", "D"], ["G", "H", "I"], ["L", "M", "N"]]
            ],
            'spare_parts' => [
                'Default' => [["B", "C", "D"], ["G", "H", "I"], ["L", "M", "N"]],
                'Housing Original Pulled' => [["B", "C", "D"], ["H", "I", "J"]]
            ],
            'wholesale' => [
                'Default' => [["B", "C", "D"], ["G", "H", "I"], ["L", "M", "N"]],
                'Housing Original Pulled' => [["B", "C", "D"], ["H", "I", "J"]],
                'Cases' => [["B", "C", "D"]],
                'Apple Watchband' =>[["A", "B", "C"]],
                'Baseus' =>[["A", "B", "C"]]
            ]
        ];

        // Get the configuration for the selected template type, or use standard if not found
        $templateConfig = $columnConfigs[$templateType] ?? $columnConfigs['standard'];
        $this->_logger->info('Template type: ' . $templateType . ', Available sheet configs: ' . implode(', ', array_keys($templateConfig)));

        try {
            // Log the currency being used
            $this->_logger->info('Processing Excel file with currency: ' . $currency);

            // Process Excel file using PhpSpreadsheet
            $reader = new XlsxReader();
            $spreadsheet = $reader->load($filePath);

            // Extract product data - will collect from all sheets
            $productsData = [];

            // Get all sheet names
            $allSheets = $spreadsheet->getSheetNames();
            $this->_logger->info('Available sheets: ' . implode(', ', $allSheets));

            // Process all sheets in the workbook
            foreach ($allSheets as $sheetName) {
                // Skip sheets that look like system sheets
                if (stripos($sheetName, 'pricelist') !== false) {
                    $this->_logger->info('Skipping pricelist sheet: ' . $sheetName);
                    continue;
                }

                $sheet = $spreadsheet->getSheetByName($sheetName);
                $highestRow = $sheet->getHighestRow();
                $this->_logger->info('Processing sheet: ' . $sheetName . ' with ' . $highestRow . ' rows');

                // Get sheet-specific column groups if available, otherwise use default
                // First try exact match, then case-insensitive match
                $configKey = 'Default';
                if (isset($templateConfig[$sheetName])) {
                    $configKey = $sheetName;
                    $this->_logger->info('Found exact match for sheet: ' . $sheetName);
                } else {
                    // Try case-insensitive match
                    foreach (array_keys($templateConfig) as $key) {
                        if (strcasecmp($key, $sheetName) === 0) {
                            $configKey = $key;
                            $this->_logger->info('Found case-insensitive match for sheet: ' . $sheetName . ' -> ' . $key);
                            break;
                        }
                    }
                    if ($configKey === 'Default') {
                        $this->_logger->info('No specific config found for sheet: ' . $sheetName . ', using Default');
                    }
                }
                $sheetColumnGroups = $templateConfig[$configKey];
                $this->_logger->info('Using configuration for sheet: ' . $configKey);

                // Process each column group for this sheet
                foreach ($sheetColumnGroups as $cols) {
                    $skuCol = $cols[0];
                    $artCol = $cols[1];
                    $priceCol = $cols[2];

                    $this->_logger->info('Using columns: SKU=' . $skuCol . ', Art=' . $artCol . ', Price=' . $priceCol);

                    for ($row = 2; $row <= $highestRow; $row++) {
                        $skuValue = $sheet->getCell($skuCol . $row)->getValue();
                        $artValue = $sheet->getCell($artCol . $row)->getValue();
                        $price = $sheet->getCell($priceCol . $row)->getValue();

                        // Handle null values
                        $sku = is_null($skuValue) ? '' : trim((string)$skuValue);
                        $art = is_null($artValue) ? '' : trim((string)$artValue);

                        if ($row <= 5) {
                            $this->_logger->info('Row ' . $row . ' data: SKU=' . $sku . ', Art=' . $art . ', Price=' . $price);
                        }

                        if ($sku && $art && is_numeric($price) && $price > 0) {
                            // Get current price from Magento based on selected currency
                            $magentoPrice = $this->getProductPrice($sku, $currency);

                            if ($magentoPrice > 0) {
                                // Store the original prices for reference
                                $originalMagentoPrice = $magentoPrice;
                                $originalExcelPrice = $price;

                                // Calculate discount percentage
                                $discount = round(($magentoPrice - $price) * 100 / $magentoPrice, 2);

                                $this->_logger->info('Found product: SKU=' . $sku . ', MagentoPrice=' . $magentoPrice . ', ExcelPrice=' . $price . ', Discount=' . $discount . ', Currency=' . $currency);

                                // Add to products data
                                $productsData[] = [
                                    'sku' => $sku,
                                    'name' => $art,
                                    'discount' => $discount,
                                    'sheet' => $sheetName,
                                    'magento_price' => $magentoPrice,
                                    'excel_price' => $price,
                                    'currency' => $currency
                                ];
                                $this->_logger->info('Added product from sheet ' . $sheetName . ': SKU=' . $sku . ', Magento price=' . $magentoPrice . ', Excel price=' . $price . ', Discount=' . $discount . ', Currency=' . $currency);
                            } else {
                                $this->_logger->info('Product not found or price is zero: SKU=' . $sku);
                            }
                        }
                    }
                }
            }

            $this->_logger->info('Found ' . count($productsData) . ' products to update');

            // Update pricelist with discounts
            $updatedSkus = $this->updatePricelistDiscounts($pricelistId, $productsData);

            return $updatedSkus;
        } catch (\Exception $e) {
            $this->_logger->error('Error processing Excel: ' . $e->getMessage());
            throw new \Exception("Error processing Excel file: " . $e->getMessage());
        }
    }

    /**
     * Get product price from Magento based on currency
     *
     * @param string $sku
     * @param string $currency
     * @return float
     */
    private function getProductPrice($sku, $currency = 'SEK')
    {
        try {
            // Determine store ID based on currency
            $storeId = Common::STORE_ID_DEFAULT; // Default store view
            if ($currency === 'EUR') {
                $storeId = Common::STORE_ID_EUR; // EUR store view
                $this->_logger->info('Using EUR store (ID: ' . $storeId . ') for product: ' . $sku);
            } else if ($currency === 'SEK') {
                $storeId = Common::STORE_ID_SEK; // SEK store view
                $this->_logger->info('Using SEK store (ID: ' . $storeId . ') for product: ' . $sku);
            }

            // Load the product with the specific store ID to get the correct price in that store's currency
            $product = $this->productRepository->get($sku, false, $storeId);
            $price = $product->getPrice();
            
            // Debug log to verify the price
            $this->_logger->info('Retrieved price for product ' . $sku . ' in store ' . $storeId . ' (' . $currency . '): ' . $price);
            
            // Verify if the price is in the correct currency by checking the website ID
            $websiteId = $this->storeManager->getStore($storeId)->getWebsiteId();
            $this->_logger->info('Store ' . $storeId . ' is on website ID: ' . $websiteId);
            
            // Force currency-specific price retrieval
            if ($currency === 'EUR') {
                // Get EUR price directly from the product
                $priceAttribute = $product->getResource()->getAttribute('price');
                if ($priceAttribute) {
                    $priceInStore = $priceAttribute->getFrontend()->getValue($product);
                    $this->_logger->info('EUR price from attribute: ' . $priceInStore);
                    if ($priceInStore > 0) {
                        $price = $priceInStore;
                    }
                }
            }

            return (float)$price;
        } catch (\Exception $e) {
            $this->_logger->error('Error getting price for SKU ' . $sku . ': ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Update pricelist discounts
     *
     * @param int $pricelistId
     * @param array $productsData
     * @return array
     */
    private function updatePricelistDiscounts($pricelistId, $productsData)
    {
        $connection = $this->resourceConnection->getConnection();
        $table = $this->resourceConnection->getTableName('fhr_price_pricelists_products');

        $updatedSkus = [];

        foreach ($productsData as $product) {
            $sku = $product['sku'];
            $discount = $product['discount'];
            $magentoPrice = $product['magento_price'];
            $excelPrice = $product['excel_price'];
            $currency = $product['currency'];

            $this->_logger->info("Saving product $sku to pricelist: Magento price = $magentoPrice, Excel price = $excelPrice, Discount = $discount%, Currency = $currency");

            // Check if record exists
            $select = $connection->select()
                ->from($table)
                ->where('pricelists_id = ?', $pricelistId)
                ->where('product_sku = ?', $sku);
            $existingRecord = $connection->fetchRow($select);

            if ($existingRecord) {
                // Update existing record
                $connection->update(
                    $table,
                    ['percent' => $discount],
                    [
                        'pricelists_id = ?' => $pricelistId,
                        'product_sku = ?' => $sku
                    ]
                );
            } else {
                // Insert new record
                $connection->insert(
                    $table,
                    [
                        'pricelists_id' => $pricelistId,
                        'product_sku' => $sku,
                        'percent' => $discount
                    ]
                );
            }

            $updatedSkus[] = $sku;
        }

        return $updatedSkus;
    }
}
