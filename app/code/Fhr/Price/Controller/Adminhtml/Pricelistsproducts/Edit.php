<?php


namespace Fhr\Price\Controller\Adminhtml\Pricelistsproducts;

/**
 * Class Edit
 *
 * @package Fhr\Price\Controller\Adminhtml\Pricelistsproducts
 */
class Edit extends \Fhr\Price\Controller\Adminhtml\Pricelistsproducts
{
    const ADMIN_RESOURCE = 'Fhr_Price::Pricelistsproducts_update';

    protected $resultPageFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        // 1. Get ID and create model
        $id = $this->getRequest()->getParam('entity_id');
        $model = $this->_objectManager->create(\Fhr\Price\Model\Pricelistsproducts::class);
        
        // 2. Initial checking
        if ($id) {
            $model->load($id);
            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This Pricelistsproducts no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }
        $this->_coreRegistry->register('fhr_price_pricelistsproducts', $model);
        
        // 3. Build edit form
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Pricelistsproducts') : __('New Pricelistsproducts'),
            $id ? __('Edit Pricelistsproducts') : __('New Pricelistsproducts')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Pricelistsproductss'));
        $resultPage->getConfig()->getTitle()->prepend($model->getId() ? __('Edit Pricelistsproducts %1', $model->getId()) : __('New Pricelistsproducts'));
        return $resultPage;
    }
}
