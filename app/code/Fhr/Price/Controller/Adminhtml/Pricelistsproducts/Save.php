<?php


namespace Fhr\Price\Controller\Adminhtml\Pricelistsproducts;

use Fhr\Price\Model\PricelistsCalc;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Exception\LocalizedException;
use Fhr\Price\Model\Customprice;

/**
 * Class Save
 *
 * @package Fhr\Price\Controller\Adminhtml\Pricelistsproducts
 */
class Save extends \Magento\Backend\App\Action
{
    const ADMIN_RESOURCE = 'Fhr_Price::Pricelistsproducts_save';

    protected $dataPersistor;
    /**
     * @var PricelistsCalc
     */
    private $pricelistsCalc;
    /**
     * @var Customprice
     */
    private $customprice;

    /**
     * @param Context $context
     * @param DataPersistorInterface $dataPersistor
     * @param PricelistsCalc $pricelistsCalc
     * @param Customprice $customprice
     */
    public function __construct(
        Context $context,
        DataPersistorInterface $dataPersistor,
        PricelistsCalc $pricelistsCalc,
        Customprice $customprice
    ) {
        parent::__construct($context);
        $this->dataPersistor = $dataPersistor;
        $this->pricelistsCalc = $pricelistsCalc;
        $this->customprice = $customprice;
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->processData($this->getRequest()->getPostValue());
        if ($data) {
            $id = $this->getRequest()->getParam('entity_id');
        
            $model = $this->_objectManager->create(\Fhr\Price\Model\Pricelistsproducts::class)->load($id);
            if (!$model->getId() && $id) {
                $this->messageManager->addErrorMessage(__('This Pricelistsproducts no longer exists.'));
                return $resultRedirect->setPath('*/*/');
            }

            $data['product_id'] = $this->pricelistsCalc->getProductId($data['product_sku']);
            $data['product_name'] = $this->pricelistsCalc->getProductName($data['product_sku']);
            $model->setData($data);
        
            try {
                $model->save();
                $this->customprice->clearCatalogCache($data['product_id']);
                $this->messageManager->addSuccessMessage(__('You saved the Pricelistsproducts.'));
                $this->dataPersistor->clear('fhr_price_pricelistsproducts');
        
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['entity_id' => $model->getId()]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while saving the Pricelistsproducts.'));
            }
        
            $this->dataPersistor->set('fhr_price_pricelistsproducts', $data);
            return $resultRedirect->setPath('*/*/edit', ['entity_id' => $this->getRequest()->getParam('entity_id')]);
        }
        return $resultRedirect->setPath('*/*/');
    }

    /**
     * @param array $data
     * @return array
     */
    public function processData(array $data)
    {
        if (isset($data['product_sku'])) {
            $data['product_sku'] = trim($data['product_sku']);
        }
        return $data;
    }
}
