<?php


namespace Fhr\Price\Controller\Adminhtml\Pricelistsproducts;

/**
 * Class NewAction
 *
 * @package Fhr\Price\Controller\Adminhtml\Pricelistsproducts
 */
class NewAction extends \Fhr\Price\Controller\Adminhtml\Pricelistsproducts
{
    const ADMIN_RESOURCE = 'Fhr_Price::Pricelistsproducts_new';

    protected $resultForwardFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Backend\Model\View\Result\ForwardFactory $resultForwardFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Backend\Model\View\Result\ForwardFactory $resultForwardFactory
    ) {
        $this->resultForwardFactory = $resultForwardFactory;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * New action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Forward $resultForward */
        $resultForward = $this->resultForwardFactory->create();
        return $resultForward->forward('edit');
    }
}
