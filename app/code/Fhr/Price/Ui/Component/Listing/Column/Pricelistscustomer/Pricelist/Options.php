<?php


namespace Fhr\Price\Ui\Component\Listing\Column\Pricelistscustomer\Pricelist;

use Fhr\Price\Api\PricelistsRepositoryInterface;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Fhr\Base\Model\Cache\Type as CacheType;

class Options implements OptionSourceInterface
{
    const FHR_PRICE_CACHE_KEY = 'fhr_price_pricelistscustomer_pricelist_options';
    const FHR_PRICE_CACHE_TAG = 'fhr_price_pricelistscustomer_pricelist_options';
    /**
     * @var array
     */
    protected $options;
    /**
     * @var pricelistsRepositoryInterface
     */
    private $pricelistsRepository;
    /**
     * @var FilterBuilder
     */
    private $filterBuilder;
    /**
     * @var FilterGroupBuilder
     */
    private $filterGroupBuilder;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    /**
     * @var SortOrderBuilder
     */
    private $sortOrderBuilder;
    /**
     * @var CacheInterface
     */
    private $cache;
    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * Options constructor.
     * @param pricelistsRepositoryInterface $pricelistsRepository
     * @param FilterBuilder $filterBuilder
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param FilterGroupBuilder $filterGroupBuilder
     * @param SortOrderBuilder $sortOrderBuilder
     * @param CacheInterface $cache
     * @param SerializerInterface $serializer
     */
    public function __construct(
        PricelistsRepositoryInterface $pricelistsRepository,
        FilterBuilder $filterBuilder,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        SortOrderBuilder $sortOrderBuilder,
        CacheInterface $cache,
        SerializerInterface $serializer
    ) {
        $this->pricelistsRepository = $pricelistsRepository;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->cache = $cache;
        $this->serializer = $serializer;
    }

    /**
     * @return array|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function toOptionArray()
    {
        if (!empty($this->cache->load(self::FHR_PRICE_CACHE_KEY))) {
            $this->options = $this->serializer->unserialize($this->cache->load(self::FHR_PRICE_CACHE_KEY));
        }

        if ($this->options === null) {
//            $orderFilter = $this->filterBuilder
//            ->setField('status')
//            ->setValue(1)
//            ->setConditionType('eq')
//            ->create();
//            $orderFilterGroup = $this->filterGroupBuilder->setFilters([$orderFilter])->create();
            $sortOrder = $this->sortOrderBuilder->setField('name')->setDirection('ASC')->create();
            $searchCriteria = $this->searchCriteriaBuilder
//                ->setFilterGroups([$orderFilterGroup])
                ->setSortOrders([$sortOrder])
                ->create();
            $pricelistsList = $this->pricelistsRepository->getList($searchCriteria)->getItems();
            $options = [];
            /** @var \Fhr\Price\Api\Data\PricelistsInterface $pricelist */
            foreach ($pricelistsList as $pricelist) {
                $options[] = [
                'value' => $pricelist->getPricelistsId(),
                'label' => $pricelist->getName()
                    .' ['.$pricelist->getPricelistsId().']'
                ];
            }
            $serializedData = $this->serializer->serialize($options);
            $this->cache->save(
                $serializedData,
                self::FHR_PRICE_CACHE_KEY,
                [CacheType::CACHE_TAG, self::FHR_PRICE_CACHE_TAG]
            );
            $this->options = $options;
        }
        return $this->options;
    }
}
