<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                fhr_price_pricelists_listing.fhr_price_pricelists_listing_data_source
            </item>
        </item>
    </argument>
    <settings>
        <spinner>fhr_price_pricelists_columns</spinner>
        <deps>
            <dep>fhr_price_pricelists_listing.fhr_price_pricelists_listing_data_source</dep>
        </deps>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add new Pricelists</label>
            </button>
        </buttons>
    </settings>
    <dataSource component="Magento_Ui/js/grid/provider" name="fhr_price_pricelists_listing_data_source">
        <settings>
            <updateUrl path="mui/index/render"/>
            <storageConfig>
                <param name="indexField" xsi:type="string">pricelists_id</param>
            </storageConfig>
        </settings>
        <aclResource>Fhr_Price::Pricelists</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="fhr_price_pricelists_listing_data_source">
            <settings>
                <requestFieldName>pricelists_id</requestFieldName>
                <primaryFieldName>pricelists_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
        <filters name="listing_filters">
            <filterSelect name="store_id" provider="${ $.parentName }">
                <settings>
                    <captionValue>0</captionValue>
                    <options class="Fhr\Price\Ui\Component\Listing\Column\Stores\Options"/>
                    <label translate="true">Store View</label>
                    <dataScope>store_id</dataScope>
                    <imports>
                        <link name="visible">componentType = column, index = ${ $.index }:visible</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
    </listingToolbar>
    <columns name="fhr_price_pricelists_columns">
        <settings>
            <editorConfig>
                <param name="selectProvider" xsi:type="string">
                    fhr_price_pricelists_listing.fhr_price_pricelists_listing.fhr_price_pricelists_columns.ids
                </param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="indexField" xsi:type="string">pricelists_id</param>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" path="fhr_price/Pricelists/inlineEdit" xsi:type="url"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">
                        fhr_price_pricelists_listing.fhr_price_pricelists_listing.fhr_price_pricelists_columns_editor
                    </item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>pricelists_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="pricelists_id">
            <settings>
                <filter>text</filter>
                <sorting>asc</sorting>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="name">
            <settings>
                <filter>text</filter>
                <label translate="true">Name</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="store_id" class="Magento\Store\Ui\Component\Listing\Column\Store">
            <settings>
                <label translate="true">Store View</label>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <sortable>false</sortable>
            </settings>
        </column>
        <column name="status" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <options class="Fhr\Price\Ui\Component\Listing\Column\Status\Options"/>
                <dataType>select</dataType>
                <label translate="true">Status</label>
                <editor>
                    <editorType>select</editorType>
                </editor>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created</label>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated</label>
            </settings>
        </column>
        <column name="customerinpricelist"
                class="Fhr\Price\Ui\Component\Listing\Column\Customerinpricelist\Customerinpricelist">
            <settings>
                <label translate="true">Customer in pricelist</label>
                <sortable>false</sortable>
            </settings>
        </column>
        <column name="productsinpricelist"
                class="Fhr\Price\Ui\Component\Listing\Column\Productsinpricelist\Productsinpricelist">
            <settings>
                <label translate="true">Products in pricelist</label>
                <sortable>false</sortable>
            </settings>
        </column>
        <actionsColumn class="Fhr\Price\Ui\Component\Listing\Column\PricelistsActions" name="actions">
            <settings>
                <indexField>pricelists_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>107</resizeDefaultWidth>
            </settings>
        </actionsColumn>
    </columns>
</listing>
