<?php


namespace Fhr\Price\Api\Data;

/**
 * Interface PricelistsproductsSearchResultsInterface
 *
 * @package Fhr\Price\Api\Data
 */
interface PricelistsproductsSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Pricelistsproducts list.
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface[]
     */
    public function getItems();

    /**
     * Set product_id list.
     * @param \Fhr\Price\Api\Data\PricelistsproductsInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
