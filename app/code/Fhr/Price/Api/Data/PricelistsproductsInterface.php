<?php


namespace Fhr\Price\Api\Data;

/**
 * Interface PricelistsproductsInterface
 *
 * @package Fhr\Price\Api\Data
 */
interface PricelistsproductsInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{

    const ENTITY_ID = 'entity_id';
    const PRICELISTS_ID = 'pricelists_id';
    const PRODUCT_ID = 'product_id';
    const PRODUCT_SKU = 'product_sku';
    const PRODUCT_NAME = 'product_name';
    const PERCENT = 'percent';
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    /**
     * Get entity_id
     * @return int|null
     */
    public function getEntityId();

    /**
     * Set entity_id
     * @param int $entityId
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setEntityId($entityId);

    /**
     * Get pricelist_id
     * @return int|null
     */
    public function getPricelistsId();

    /**
     * Set pricelist_id
     * @param int $pricelistsId
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setPricelistsId($pricelistsId);

    /**
     * Get product_id
     * @return int|null
     */
    public function getProductId();

    /**
     * Set product_id
     * @param int $productId
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setProductId($productId);

    /**
     * Get product_sku
     * @return string|null
     */
    public function getProductSku();

    /**
     * Set product_sku
     * @param string $productSku
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setProductSku($productSku);

    /**
     * Get product_name
     * @return string|null
     */
    public function getProductName();

    /**
     * Set product_name
     * @param string $productName
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setProductName($productName);

    /**
     * Get percent
     * @return string|null
     */
    public function getPercent();

    /**
     * Set percent
     * @param string $percent
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setPercent($percent);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setCreatedAt($createdAt);

    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt();

    /**
     * Set updated_at
     * @param string $updatedAt
     * @return \Fhr\Price\Api\Data\PricelistsproductsInterface
     */
    public function setUpdatedAt($updatedAt);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Fhr\Price\Api\Data\PricelistsproductsExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Fhr\Price\Api\Data\PricelistsproductsExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Fhr\Price\Api\Data\PricelistsproductsExtensionInterface $extensionAttributes
    );
}
