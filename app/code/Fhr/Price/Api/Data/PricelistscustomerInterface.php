<?php


namespace Fhr\Price\Api\Data;

/**
 * Interface PricelistscustomerInterface
 *
 * @package Fhr\Price\Api\Data
 */
interface PricelistscustomerInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{


    const ENTITY_ID = 'entity_id';
    const PRICELISTS_ID = 'pricelists_id';
    const CUSTOMER_ID = 'customer_id';
    const WHEN_ADD_TO_PRICE = 'when_add_to_price';
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    /**
     * Get entity_id
     * @return int|null
     */
    public function getEntityId();

    /**
     * Set entity_id
     * @param int $entityId
     * @return \Fhr\Price\Api\Data\PricelistscustomerInterface
     */
    public function setEntityId($entityId);

    /**
     * Get pricelist_id
     * @return int|null
     */
    public function getPricelistId();

    /**
     * Set pricelist_id
     * @param int $pricelistId
     * @return \Fhr\Price\Api\Data\PricelistscustomerInterface
     */
    public function setPricelistId($pricelistId);

    /**
     * Get customer_id
     * @return int |null
     */
    public function getCustomerId();

    /**
     * Set customer_id
     * @param int $customerId
     * @return \Fhr\Price\Api\Data\PricelistscustomerInterface
     */
    public function setCustomerId($customerId);

    /**
     * Get when_add_to_price
     * @return string|null
     */
    public function getWhenAddToPrice();

    /**
     * Set when_add_to_price
     * @param $whenAddToPrice
     * @return \Fhr\Price\Api\Data\PricelistscustomerInterface
     */
    public function setWhenAddToPrice($whenAddToPrice);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Fhr\Price\Api\Data\PricelistscustomerInterface
     */
    public function setCreatedAt($createdAt);

    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt();

    /**
     * Set updated_at
     * @param string $updatedAt
     * @return \Fhr\Price\Api\Data\PricelistscustomerInterface
     */
    public function setUpdatedAt($updatedAt);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Fhr\Price\Api\Data\PricelistscustomerExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Fhr\Price\Api\Data\PricelistscustomerExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Fhr\Price\Api\Data\PricelistscustomerExtensionInterface $extensionAttributes
    );
}
