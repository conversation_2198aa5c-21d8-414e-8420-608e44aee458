<?php


namespace Fhr\Price\Model;

use Fhr\Price\Api\Data\PricelistsproductsInterface;
use Fhr\Price\Api\Data\PricelistsproductsInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;

/**
 * Class Pricelistsproducts
 *
 * @package Fhr\Price\Model
 */
class Pricelistsproducts extends \Magento\Framework\Model\AbstractModel
{

    protected $pricelistsproductsDataFactory;

    protected $dataObjectHelper;

    protected $_eventPrefix = 'fhr_price_pricelistsproducts';

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param PricelistsproductsInterfaceFactory $pricelistsproductsDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param \Fhr\Price\Model\ResourceModel\Pricelistsproducts $resource
     * @param \Fhr\Price\Model\ResourceModel\Pricelistsproducts\Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        PricelistsproductsInterfaceFactory $pricelistsproductsDataFactory,
        DataObjectHelper $dataObjectHelper,
        \Fhr\Price\Model\ResourceModel\Pricelistsproducts $resource,
        \Fhr\Price\Model\ResourceModel\Pricelistsproducts\Collection $resourceCollection,
        array $data = []
    ) {
        $this->pricelistsproductsDataFactory = $pricelistsproductsDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve pricelistsproducts model with pricelistsproducts data
     * @return PricelistsproductsInterface
     */
    public function getDataModel()
    {
        $pricelistsproductsData = $this->getData();
        
        $pricelistsproductsDataObject = $this->pricelistsproductsDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $pricelistsproductsDataObject,
            $pricelistsproductsData,
            PricelistsproductsInterface::class
        );
        
        return $pricelistsproductsDataObject;
    }
}
