<?php


namespace Fhr\Price\Model;

use Fhr\Price\Api\Data\PricelistsInterface;
use Fhr\Price\Api\Data\PricelistsInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;

/**
 * Class Pricelists
 *
 * @package Fhr\Price\Model
 */
class Pricelists extends \Magento\Framework\Model\AbstractModel
{

    protected $pricelistsDataFactory;

    protected $dataObjectHelper;

    protected $_eventPrefix = 'fhr_price_pricelists';

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param PricelistsInterfaceFactory $pricelistsDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param \Fhr\Price\Model\ResourceModel\Pricelists $resource
     * @param \Fhr\Price\Model\ResourceModel\Pricelists\Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        PricelistsInterfaceFactory $pricelistsDataFactory,
        DataObjectHelper $dataObjectHelper,
        \Fhr\Price\Model\ResourceModel\Pricelists $resource,
        \Fhr\Price\Model\ResourceModel\Pricelists\Collection $resourceCollection,
        array $data = []
    ) {
        $this->pricelistsDataFactory = $pricelistsDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve pricelists model with pricelists data
     * @return PricelistsInterface
     */
    public function getDataModel()
    {
        $pricelistsData = $this->getData();
        
        $pricelistsDataObject = $this->pricelistsDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $pricelistsDataObject,
            $pricelistsData,
            PricelistsInterface::class
        );
        
        return $pricelistsDataObject;
    }
}
