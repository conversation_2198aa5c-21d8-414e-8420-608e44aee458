<?php


namespace Fhr\Price\Model;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Customer\Model\SessionFactory as CustomerSessionFactory;
use Fhr\Price\Api\PricelistscustomerRepositoryInterface;
use Fhr\Price\Api\PricelistsproductsRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Http\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\App\Cache\TypeListInterface;
use Magento\Framework\App\Cache\Frontend\Pool;
use Fhr\Base\Api\CommonInterface;
use Fhr\Base\Model\Mail\SendEmail;

class Customprice
{
    const XMP_PATH_ENABLED = 'fhrprice/general/enabled';
    const XMP_PATH_EMAILS = 'fhrprice/general/emails';
    const XMP_PATH_TEMPLATE = 'fhrprice/general/template';
    const XMP_PATH_FTP = 'fhrprice/general/ftp';
    const XMP_PATH_CSV = 'fhrprice/general/csv';
    const FILE_TYPE = 'text/csv';

    private $customerSession;
    private $pricelistsCustomerRepository;
    private $pricelistsProductsRepository;
    private $searchCriteriaBuilder;
    /**
     * @var Context
     */
    private $httpContext;
    /**
     * @var \Magento\Catalog\Model\ProductRepository
     */
    private $productRepository;
    /**
     * @var array
     */
    private $customersPricelistsIds;
    /**
     * @var TimezoneInterface
     */
    private $localeDate;
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    /**
     * @var CacheInterface
     */
    private $cacheManager;
    /**
     * @var TypeListInterface
     */
    private $cacheTypeList;
    /**
     * @var Pool
     */
    private $cacheFrontendPool;
    /**
     * @var CommonInterface
     */
    private $common;
    /**
     * @var SendEmail
     */
    private $sendEmail;

    /**
     * Customprice constructor.
     * @param CustomerSessionFactory $customerSession
     * @param PricelistscustomerRepositoryInterface $pricelistsCustomerRepository
     * @param PricelistsproductsRepositoryInterface $pricelistsProductsRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param Context $httpContext
     * @param TimezoneInterface $localeDate
     * @param StoreManagerInterface $storeManager
     * @param ProductRepositoryInterface $productRepository
     * @param TypeListInterface $cacheTypeList
     * @param Pool $cacheFrontendPool
     * @param CacheInterface $cacheManager
     * @param CommonInterface $common
     * @param SendEmail $sendEmail
     */
    public function __construct(
        CustomerSessionFactory $customerSession,
        PricelistscustomerRepositoryInterface $pricelistsCustomerRepository,
        PricelistsproductsRepositoryInterface $pricelistsProductsRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        Context $httpContext,
        TimezoneInterface $localeDate,
        StoreManagerInterface $storeManager,
        ProductRepositoryInterface $productRepository,
        TypeListInterface $cacheTypeList,
        Pool $cacheFrontendPool,
        CacheInterface $cacheManager,
        CommonInterface $common,
        SendEmail $sendEmail
    ) {
        $this->customerSession = $customerSession->create();
        $this->pricelistsCustomerRepository = $pricelistsCustomerRepository;
        $this->pricelistsProductsRepository = $pricelistsProductsRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->httpContext = $httpContext;
        $this->customersPricelistsIds = [];
        $this->localeDate = $localeDate;
        $this->storeManager = $storeManager;
        $this->productRepository = $productRepository;
        $this->cacheManager = $cacheManager;
        $this->cacheTypeList = $cacheTypeList;
        $this->cacheFrontendPool = $cacheFrontendPool;
        $this->common = $common;
        $this->sendEmail = $sendEmail;
    }

    /**
     * @return int|null
     */
    public function getCurrentCustomerId()
    {
        if ($this->isCustomerLoggedIn()) {
            return $this->customerSession->getCustomerId();
        }
        return 0;
    }

    /**
     * @param int $customerId
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCustomerPricelistsIds($customerId = 0)
    {
        if (empty($this->customersPricelistsIds)) {
            $customersPricelistsIds = [];
            ($customerId > 0) ? $customerId = $customerId : $customerId = $this->getCurrentCustomerId();
            $searchCriteria = $this->searchCriteriaBuilder->addFilter(
                'customer_id',
                $customerId
            )->create();
            $items = $this->pricelistsCustomerRepository->getList($searchCriteria)->getItems();
            foreach ($items as $item) {
                $customersPricelistsIds[] = $item->getPricelistId();
            }
            $this->customersPricelistsIds = $customersPricelistsIds;
        }
        return $this->customersPricelistsIds;
    }

    /**
     * @param $productSku
     * @param int $customerId
     * @return int|mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCustomerPercentForProduct($productSku, $customerId = 0)
    {
        $customersPricelistsIds = $this->getCustomerPricelistsIds($customerId);
        if (!empty($customersPricelistsIds)) {
            return $this->getPercentForPricelist($customersPricelistsIds, $productSku);
        }
        return 0;
    }

    /**
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return $this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH);
    }

    /**
     * @param \Magento\Catalog\Model\Product $product
     * @return bool
     * @throws NoSuchEntityException
     */
    public function isSpecialPriceActive($product)
    {
        // Product has special price check if it active date range then don't apply pricelist price
        if (!empty($product->getSpecialPrice())) {
            return (bool) $this->localeDate->isScopeDateInInterval(
                $this->storeManager->getStore()->getId(),
                $product->getSpecialFromDate(),
                $product->getSpecialToDate()
            );
        }
        return false;
    }

    /**
     * @param $currentProductPrice
     * @param $customerId
     * @param $productSku
     * @return float|int
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getPriceForCustomer($currentProductPrice, $productSku, $customerId = 0)
    {
        $percent = $this->getCustomerPercentForProduct($productSku, $customerId);
        if (!empty($percent)) {
            $currentProductPrice = $currentProductPrice - ($currentProductPrice * $percent / 100);
        }
        return $currentProductPrice;
    }

    /**
     * @param int $priceListId
     * @param string $sku
     * @param int|null $storeId
     * @return float|int|null
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getPriceForPriceList($priceListId, $sku, $storeId = null)
    {
        $percent = $this->getPercentForPricelist($priceListId, $sku);
        $product = $this->productRepository->get($sku, false, $storeId);
        $price = $product->getPrice();
        if (!empty($percent)) {
            $price = $price - ($price * $percent / 100);
        }
        return $price;
    }

    /**
     * @param $priceListId
     * @param $sku
     * @return int|mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getPercentForPricelist($priceListIds, $sku)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(
                'pricelists_id',
                $priceListIds,
                'in'
            )->addFilter(
                'product_sku',
                $sku
            )->create();
        $items = $this->pricelistsProductsRepository->getList($searchCriteria)->getItems();
        if (!empty($items[0])) {
            //@TODO method getPercent return null :( Fhr\Price\Model\Data\Pricelistsproducts
            $result = $items[0]->__toArray();
            return $result['percent'];
        }
        return 0;
    }

    /**
     * @param int $productId
     */
    public function clearCatalogCache(int $productId = 0)
    {
        if ($productId > 0) {
            $this->cacheManager->clean([
                'catalog_product_' . $productId,
                \Magento\Catalog\Model\Category::CACHE_TAG,
                \Magento\Framework\App\Cache\Type\Block::TYPE_IDENTIFIER
            ]);
        }
        $_types = ['full_page','block_html'];
        foreach ($_types as $type) {
            $this->cacheTypeList->cleanType($type);
        }
        // Take too long time on save :( Looks work fine without
//        foreach ($this->cacheFrontendPool as $cacheFrontend) {
//            $cacheFrontend->getBackend()->clean();
//        }
    }

    public function sendMail($file, $subject, $message, $emailsArr, $deleteAttachFlag = true)
    {
        $templateId = $this->common->getConfigValue(self::XMP_PATH_TEMPLATE);

            $toName = $emailsArr['name'];
            $toEmail = $emailsArr['email'];
                $templateVars = [
                    'subject' => $subject,
                    'customer_name' => $toName,
                    'message' => $message
                ];
                $attachment = [
                    'content' => file_get_contents($file),
                    'fileName' => basename($file),
                    'fileType' => self::FILE_TYPE
                ];
                $this->sendEmail->sendEmail($templateId, $toEmail, $toName, $templateVars, $attachment);

                if ($deleteAttachFlag) {
                    unlink($file);
                }
    }

    /**
     * get Emails from config
     * @return array
     */
    public function getConfEmails()
    {
        $emails = $this->common->getConfigValue(self::XMP_PATH_EMAILS);
        $emails = preg_replace_callback("/[\r\n]/", function($matches) { return "\n"; }, $emails);
        $emails = explode("\n", $emails);
        $result = [];
        if (!empty($emails)) {
            foreach ($emails as $idEmailName) {
                $line = explode(';', $idEmailName);
                if (count($line) == 3) {
                    $result[(int)$line[0]] = [
                        'email' => trim($line[1]),
                        'name' => trim($line[2])
                    ];
                }
            }
            return $result;
        }
        return [];
    }

    /**
     * get customers ID's from config
     * @return array
     */
    public function getConfCsv()
    {
        $csv = $this->common->getConfigValue(self::XMP_PATH_CSV);
        $csv = preg_replace_callback("/[\r\n]/", function($matches) { return ";"; }, $csv);
        $csv = explode(';', $csv);
        $result = [];
        foreach ($csv as $customerId) {
            if ((int)$customerId > 0) {
                $result[] = $customerId;
            }
        }
        return $result;
    }

    /**
     * get FTP credentials from config
     * @return array
     */
    public function getConfFtp()
    {
        $ftp = $this->common->getConfigValue(self::XMP_PATH_FTP);
        $ftp = preg_replace_callback("/[\r\n]/", function($matches) { return "\n"; }, $ftp);
        $ftp = explode("\n", $ftp);
        $result = [];
        if (!empty($ftp)) {
            foreach ($ftp as $idHostUserPwdPassive) {
                $line = explode(';', $idHostUserPwdPassive);
                if (count($line) == 5) {
                    $passive = trim($line[4]) == 'true';
                    $result[(int)$line[0]] = [
                        'host' => trim($line[1]),
                        'user' => trim($line[2]),
                        'password' => $line[3],
                        'passive' => $passive
                    ];
                }
            }
            return $result;
        }
        return [];
    }

    /**
     * @param Product $product
     * @param int $customerQty
     * @return mixed|null
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getTierPriceForCustomer(Product $product, int $customerQty)
    {
        // Get the current customer group ID
        $customerGroupId = $this->customerSession->getCustomerGroupId();

        // Get the product's tier prices
        $tierPrices = $product->getTierPrice();

        // Find the applicable tier price for the customer group and quantity (if any)
        $applicableTierPrice = null;
        $closestDifference = null;
        foreach ($tierPrices as $tierPrice) {
            if ($tierPrice['all_groups']) {
                // Tier Price applies to all customer groups, regardless of group ID
                $priceQty = (int)$tierPrice['price_qty'];
                if ($priceQty <= $customerQty) {
                    $difference = $customerQty - $priceQty;
                    if ($closestDifference === null || $difference < $closestDifference) {
                        $applicableTierPrice = $tierPrice['price'];
                        $closestDifference = $difference;
                    }
                }
            } elseif ($tierPrice['cust_group'] == $customerGroupId) {
                // Tier Price applies to a specific customer group
                $priceQty = (int)$tierPrice['price_qty'];
                if ($priceQty <= $customerQty) {
                    $difference = $customerQty - $priceQty;
                    if ($closestDifference === null || $difference < $closestDifference) {
                        $applicableTierPrice = $tierPrice['price'];
                        $closestDifference = $difference;
                    }
                }
            }
        }
        return $applicableTierPrice;
    }
}
