<?php
declare(strict_types=1);

namespace Fhr\Price\Plugin\Backend\Magento\Sales\Block\Adminhtml\Order\Create;

use Fhr\Price\Model\Customprice;

class Items
{
    /**
     * @var Customprice
     */
    private $customprice;
    /**
     * Product constructor.
     * @param Customprice $customPrice
     */
    public function __construct(
        Customprice $customPrice
    ) {
        $this->customprice = $customPrice;
    }

    /**
     * @param \Magento\Sales\Block\Adminhtml\Order\Create\Items $subject
     * @param $result
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterGetItems(
        \Magento\Sales\Block\Adminhtml\Order\Create\Items $subject,
        $result
    ) {
        $items = $result;
        /** @var \Magento\Quote\Model\Quote\Item $item */
        foreach ($items as $item) {
            if ($this->customprice->isSpecialPriceActive($item->getProduct())) {
                return $result;
            }
            if (empty($item->getCustomPrice())) {
                $customPrice = $this->customprice->getPriceForCustomer(
                    $item->getBasePrice(),
                    $item->getSku(),
                    $subject->getCustomerId()
                );
                if ($customPrice != $item->getPrice()) {
                    $item->setCustomPrice($customPrice);
                }
            }
        }
        return $result;
    }
}
