<?php


namespace Fhr\Price\Plugin\Customer;

use Magento\Framework\App\CacheInterface;

class CustomerRepository
{
    /**
     * @var CacheInterface
     */
    private $cache;

    /**
     * CustomerRepository constructor.
     * @param CacheInterface $cache
     */
    public function __construct(
        CacheInterface $cache
    ) {
        $this->cache = $cache;
    }

    /**
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $subject
     * @param \Magento\Customer\Api\Data\CustomerInterface $result
     * @return \Magento\Customer\Api\Data\CustomerInterface
     */
    public function afterSave(
        \Magento\Customer\Api\CustomerRepositoryInterface $subject,
        \Magento\Customer\Api\Data\CustomerInterface $result
    ) {
        $this->cache->clean(
            [
                \Fhr\Price\Ui\Component\Listing\Column\Pricelistscustomer\Customer\Options::FHR_PRICE_CACHE_TAG
            ]
        );
        return $result;
    }
}
