<?php


namespace Fhr\Price\Observer\Frontend\Checkout;

use Fhr\Price\Model\Customprice;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class CartProductAddAfter
 *
 * @package Fhr\Price\Observer\Frontend\Checkout
 */
class CartProductAddAfter implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var Customprice
     */
    private $customprice;

    /**
     * CartProductAddAfter constructor.
     * @param Customprice $customPrice
     */
    public function __construct(
        Customprice $customPrice
    ) {
        $this->customprice = $customPrice;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $quote = $observer->getEvent()->getQuote();

        foreach ($quote->getAllVisibleItems() as $item) {
            $tierPrice = $this->customprice->getTierPriceForCustomer($item->getProduct(), $item->getQty());
            if ($this->customprice->isSpecialPriceActive($item->getProduct())) {
                // Special Price - hight priority by magento logic
                $price = $item->getProduct()->getSpecialPrice();
            } elseif ($tierPrice !== null) {
                // Use Tier Price after Special Price
                $price = $tierPrice;
            } else {
                // Use regular product price if no Tier Price or Special Price.
                $price = $item->getProduct()->getPrice();
            }

            $item->setCustomPrice($price);
            $item->setOriginalCustomPrice($price);
            $item->getProduct()->setIsSuperMode(true);
        }
    }
}
