<?php


namespace Fhr\Price\Cron;

use Fhr\Price\Model\Customprice;
use Fhr\Price\Model\PricelistCsvForCustomer;
use Magento\Framework\App\Filesystem\DirectoryList;

class PricelistEmail
{
    /**
     * @var PricelistCsvForCustomer
     */
    private $pricelistCsvForCustomer;
    /**
     * @var DirectoryList
     */
    private $directorylist;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;
    /**
     * @var Customprice
     */
    private $customprice;

    /**
     * PricelistEmail constructor.
     * @param PricelistCsvForCustomer $pricelistCsvForCustomer
     * @param DirectoryList $directorylist
     * @param Customprice $customprice
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        PricelistCsvForCustomer $pricelistCsvForCustomer,
        DirectoryList $directorylist,
        Customprice $customprice,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->pricelistCsvForCustomer = $pricelistCsvForCustomer;
        $this->directorylist = $directorylist;
        $this->logger = $logger;
        $this->customprice = $customprice;
    }

    public function execute()
    {
        $path = $this->directorylist->getPath('var').DIRECTORY_SEPARATOR;
        $fileExt = PricelistCsvForCustomer::FILE_EXT;
        $customers_ids = $this->customprice->getConfEmails();
        foreach ($customers_ids as $customer_id => $email) {
            $filename = "fhr_products_$customer_id";
            $this->pricelistCsvForCustomer->genCsvForCustomer((int)$customer_id, $filename);
            $subject = __('FHR Pricelist');
            $message = $subject.__('Check attachment please').'. ';
            $this->customprice->sendMail(
                $path . $filename . $fileExt,
                $subject,
                $message,
                $email
            );

        }
        $this->logger->addInfo("Cronjob pricelistEmail is executed.");
    }
}
