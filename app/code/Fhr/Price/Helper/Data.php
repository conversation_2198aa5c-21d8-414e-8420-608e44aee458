<?php


namespace Fhr\Price\Helper;

use Fhr\Price\Api\PricelistsproductsRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Fhr\Base\Model\Common;

/**
 * Class Data
 * @package Fhr\Price\Helper
 */
class Data extends AbstractHelper
{
    /**
     * @var PricelistsproductsRepositoryInterface
     */
    private $pricelistsproductsRepository;
    /**
     * @var FilterBuilder
     */
    private $filterBuilder;
    /**
     * @var FilterGroupBuilder
     */
    private $filterGroupBuilder;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    /**
     * @var SortOrderBuilder
     */
    private $sortOrderBuilder;
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;
    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    private $dataPricingHelper;

    /**
     * Data constructor.
     * @param Context $context
     * @param PricelistsproductsRepositoryInterface $pricelistsproductsRepository
     * @param FilterBuilder $filterBuilder
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param FilterGroupBuilder $filterGroupBuilder
     * @param SortOrderBuilder $sortOrderBuilder
     * @param ProductRepositoryInterface $productRepository
     * @param \Magento\Framework\Pricing\Helper\Data $dataPricingHelper
     */
    public function __construct(
        Context $context,
        PricelistsproductsRepositoryInterface $pricelistsproductsRepository,
        FilterBuilder $filterBuilder,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        SortOrderBuilder $sortOrderBuilder,
        ProductRepositoryInterface $productRepository,
        \Magento\Framework\Pricing\Helper\Data $dataPricingHelper
    ) {
        parent::__construct($context);
        $this->pricelistsproductsRepository = $pricelistsproductsRepository;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->productRepository = $productRepository;
        $this->dataPricingHelper = $dataPricingHelper;
    }

    /**
     * @param string $sku
     * @param int $storeId
     * @return float|int|null
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getLowestPriceInPricelist(string $sku, int $storeId = Common::STORE_ID_DEFAULT)
    {
        $filterProductSku = $this->filterBuilder
            ->setField('product_sku')
            ->setValue($sku)
            ->setConditionType('eq')
            ->create();
        $filterGroup = $this->filterGroupBuilder->setFilters([$filterProductSku])->create();
        $sortOrder = $this->sortOrderBuilder->setField('percent')->setDirection('desc')->create();
        $searchCriteria = $this->searchCriteriaBuilder
            ->setFilterGroups([$filterGroup])
            ->setSortOrders([$sortOrder])
            ->create();
        $items = $this->pricelistsproductsRepository->getList($searchCriteria)->getItems();
        $percent = 0;
        // Maximum percent
        foreach ($items as $item) {
            if ((float)$item->getPercent() > $percent) {
                $percent = (float)$item->getPercent();
            }
        }
        $product = $this->productRepository->get($sku, false, $storeId);
        return $this->dataPricingHelper->currency($product->getPrice() - ($product->getPrice() * $percent / 100), true, false);
    }
}
