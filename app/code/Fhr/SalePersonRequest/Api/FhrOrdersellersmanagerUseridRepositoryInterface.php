<?php
declare(strict_types=1);

namespace Fhr\SalePersonRequest\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface FhrOrdersellersmanagerUseridRepositoryInterface
{

    /**
     * Save FhrOrdersellersmanagerUserid
     * @param \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridInterface $fhrOrdersellersmanagerUserid
     * @return \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridInterface $fhrOrdersellersmanagerUserid
    );

    /**
     * Retrieve FhrOrdersellersmanagerUserid
     * @param string $fhrordersellersmanageruseridId
     * @return \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($fhrordersellersmanageruseridId);

    /**
     * Retrieve FhrOrdersellersmanagerUserid matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete FhrOrdersellersmanagerUserid
     * @param \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridInterface $fhrOrdersellersmanagerUserid
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Fhr\SalePersonRequest\Api\Data\FhrOrdersellersmanagerUseridInterface $fhrOrdersellersmanagerUserid
    );

    /**
     * Delete FhrOrdersellersmanagerUserid by ID
     * @param string $fhrordersellersmanageruseridId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($fhrordersellersmanageruseridId);
}
