<?php


namespace Fhr\TranslateHelper\Model;

use \Magento\Framework\Phrase\Renderer\TranslateFactory as TranslateRendererFactory;
use Magento\Framework\TranslateInterface;
use \Magento\Framework\TranslateInterfaceFactory as TranslateFactory;

class TranslateHelper
{
    const PREFIX_CSV_TRANSALTED = 'translated_';
    const PREFIX_CSV_NOT_TRANSALTED = 'nottranslated_';
    const CSV_EXTENSION = '.csv';
    /**
     * @var \Magento\Framework\File\Csv
     */
    private $csvProcessor;
    /**
     * @var \Magento\Framework\App\Language\Dictionary
     */
    private $dictionary;
    /**
     * @var \Magento\Framework\Filesystem
     */
    private $filesystem;
    /**
     * @var TranslateRendererFactory
     */
    private $rendererFactory;

    /**
     * @var TranslateInterfaceFactory
     */
    private $translateFactory;

    /**
     * @var array || TranslateInterface[]
     */
    private $translatorPool = [];

    /**
     * TranslateHelper constructor.
     * @param \Magento\Framework\File\Csv $csvProcessor
     * @param \Magento\Framework\App\Language\Dictionary $dictionary
     * @param \Magento\Framework\Filesystem $filesystem
     * @param TranslateRendererFactory $rendererFactory
     * @param TranslateFactory $translateFactory
     */
    public function __construct(
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Framework\App\Language\Dictionary $dictionary,
        \Magento\Framework\Filesystem $filesystem,
        TranslateRendererFactory $rendererFactory,
        TranslateFactory $translateFactory
    ) {
        $this->csvProcessor = $csvProcessor;
        $this->dictionary = $dictionary;
        $this->filesystem = $filesystem;
        $this->rendererFactory = $rendererFactory;
        $this->translateFactory = $translateFactory;
    }

    /**
     * @param string $filepath <p> Full path to file with filename
     * </p>
     * @param string $languageCode <p> Language code. Like en_US,fr_FR,sv_SE etc.
     * </p>
     * @return void
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function prepareCsv($filepath, $languageCode)
    {
        $csv = $this->csvProcessor->getData($filepath);
        $directory = $this->filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::VAR_DIR);
        $filepathTranslated = self::PREFIX_CSV_TRANSALTED.$languageCode.self::CSV_EXTENSION;
        $filepathNotTranslated = self::PREFIX_CSV_NOT_TRANSALTED.$languageCode.self::CSV_EXTENSION;
        $streamTranslated = $directory->openFile($filepathTranslated, 'w+');
        $streamNotTranslated = $directory->openFile($filepathNotTranslated, 'w+');
        foreach ($csv as $csvLine) {
            $orig = $csvLine[0];
            $trans = $this->translateByLangCode($csvLine[1], $languageCode);
            if ($orig != $trans) {
                $csvArray = [
                    $csvLine[0],
                    $trans
                ];
                $streamTranslated->writeCsv($csvArray);
            } else {
                $csvArray = [
                    $csvLine[0],
                    $csvLine[1]
                ];
                $streamNotTranslated->writeCsv($csvArray);
            }
        }
    }

    /**
     * Translate the given string in the correct local.
     *
     * @param string $string
     * @param string $langCode
     * @return string
     */
    public function translateByLangCode(string $string, string $langCode): string
    {
        $translator = $this->getTranslator($langCode);
        $orgRenderer = \Magento\Framework\Phrase::getRenderer();

        $renderer = $this->rendererFactory->create(['translator' => $translator]);
        // inject translation render
        \Magento\Framework\Phrase::setRenderer($renderer);

        //translate the given string
        $phrase = new \Magento\Framework\Phrase($string);
        $translation = (string)$phrase;

        // reset the renderer to original
        \Magento\Framework\Phrase::setRenderer($orgRenderer);

        return $translation;
    }

    /**
     * Return the translator instance by correct language.
     *
     * @param string $langCode
     * @return mixed
     */
    private function getTranslator(string $langCode)
    {
        if (!isset($this->translatorPool[$langCode])) {
            /** @var TranslateInterface $translator */
            $translator = $this->translateFactory->create();
            $translator->setLocale($langCode);
            $translator->loadData();
            $this->translatorPool[$langCode] = $translator;
        }

        return $this->translatorPool[$langCode];
    }
}
