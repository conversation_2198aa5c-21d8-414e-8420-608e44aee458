<?php

namespace Fhr\OneDomainMigration\Console\Command;

use Fhr\OneDomainMigration\Model\CustomerMigration;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FullCustomer extends Command
{
    const CUSTOMER_ID_EMAIL = 'customer_id_or_email';
    const TARGET_WEBSITE = 'target_website';

    protected $customerMigration;

    public function __construct(CustomerMigration $customerMigration)
    {
        $this->customerMigration = $customerMigration;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('fhr_onedomainmigration:fullcustomer')
            ->setDescription('Move customer from one website to another with orders converted SEK => EUR')
            ->addArgument(self::CUSTOMER_ID_EMAIL, InputArgument::REQUIRED, 'Customer ID or Email')
            ->addArgument(self::TARGET_WEBSITE, InputArgument::REQUIRED, 'Target Website Code');
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $customerIdOrEmail = $input->getArgument(self::CUSTOMER_ID_EMAIL);
        $targetWebsiteCode = $input->getArgument(self::TARGET_WEBSITE);

        try {
            $result = $this->customerMigration->moveCustomer($customerIdOrEmail, $targetWebsiteCode);
            $output->writeln('<info>' . $result . '</info>');
            return Cli::RETURN_SUCCESS;
        } catch (\Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
            return Cli::RETURN_FAILURE;
        }
    }
}

