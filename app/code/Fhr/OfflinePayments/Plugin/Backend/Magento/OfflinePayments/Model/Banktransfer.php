<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\OfflinePayments\Plugin\Backend\Magento\OfflinePayments\Model;

class Banktransfer
{

    /**
     * @param \Magento\OfflinePayments\Model\Banktransfer $subject
     * @param $result
     * @return bool
     */
    public function afterIsAvailable(
        \Magento\OfflinePayments\Model\Banktransfer $subject,
        $result
    ) {
        // Quick fix for adminhtml force enable banktransfer
        // @TODO Check why banktransfer not availible for adminhtml
        $result = true;
        return $result;
    }
}
