<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\PeriodicReports\Cron;

use Magento\Framework\Filesystem\Driver\File;

class TopProductsOrderedThisMonth
{
    protected $logger;
    /**
     * @var \Fhr\PeriodicReports\Model\TopProductsOrderedThisMonth
     */
    private $topProductsOrderedThisMonth;
    /**
     * @var File
     */
    private $fileDriver;

    /**
     * Constructor
     *
     * @param \Fhr\PeriodicReports\Model\TopProductsOrderedThisMonth $topProductsOrderedThisMonth
     * @param File $fileDriver
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Fhr\PeriodicReports\Model\TopProductsOrderedThisMonth $topProductsOrderedThisMonth,
        File $fileDriver,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->logger = $logger;
        $this->topProductsOrderedThisMonth = $topProductsOrderedThisMonth;
        $this->fileDriver = $fileDriver;
    }

    /**
     * Execute the cron
     *
     * @return void
     * @throws \Zend_Date_Exception
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute()
    {
        $currentDate = date('Y-m-d H:i:s');
        $file = $this->topProductsOrderedThisMonth->generateExcelFile($currentDate);
        if ($this->fileDriver->isExists($file)) {
            $this->topProductsOrderedThisMonth->sendMail($file, $currentDate);
        } else {
            $this->logger->addWarning(__CLASS__.'>'.__FUNCTION__.': '.__('File %1 not found', $file));
        }
        $this->logger->addInfo("Cronjob TopProductsOrderedThisMonth is executed.");
    }
}
