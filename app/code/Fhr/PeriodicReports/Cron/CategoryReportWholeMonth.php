<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\PeriodicReports\Cron;

class CategoryReportWholeMonth
{

    protected $logger;
    /**
     * @var \Fhr\PeriodicReports\Model\CategoryReport
     */
    private $categoryReport;

    /**
     * Constructor
     *
     * @param \Fhr\PeriodicReports\Model\CategoryReport $categoryReport
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Fhr\PeriodicReports\Model\CategoryReport $categoryReport,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->logger = $logger;
        $this->categoryReport = $categoryReport;
    }

    /**
     * Execute the cron
     *
     * @return void
     * @throws \Zend_Date_Exception
     */
    public function execute()
    {
        $month = date('m');
        $year = date('Y');
        --$month;
        if ($month == 0) {
            $month = 12;
            --$year;
        }
        $data = $this->categoryReport->generateData($month, $year);
        if (!empty($data)) {
            $file = $this->categoryReport->generateFile($data, $month, $year);
            if (file_exists($file)) {
                $this->categoryReport->sendMail($file, $month, $year);
            } else {
                $this->logger->addWarning(__CLASS__.'>'.__FUNCTION__.': '.__('File %1 not found', $file));
            }
        } else {
            $this->logger->addWarning(__CLASS__.'>'.__FUNCTION__.': '.__('Data is empty'));
        }

        $this->logger->addInfo("Cronjob CategoryReportWholeMonth is executed.");
    }
}
