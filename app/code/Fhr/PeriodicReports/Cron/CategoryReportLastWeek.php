<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\PeriodicReports\Cron;

class CategoryReportLastWeek
{
    protected $logger;
    /**
     * @var \Fhr\PeriodicReports\Model\CategoryReport
     */
    private $categoryReportDay;
    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime|\Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    private $dateTime;

    /**
     * CategoryReportLastWeek constructor.
     * @param \Fhr\PeriodicReports\Model\CategoryReportDay $categoryReportDay
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $dateTime
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Fhr\PeriodicReports\Model\CategoryReportDay $categoryReportDay,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $dateTime,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->logger = $logger;
        $this->categoryReportDay = $categoryReportDay;
        $this->dateTime = $dateTime;
    }

    /**
     * Execute the cron
     *
     * @return void
     * @throws \Zend_Date_Exception
     */
    public function execute()
    {
        $date = $this->dateTime->date();
        $to = $date->format('Y-m-d 23:59:59');
        $from = $date->modify('-1 week')->format('Y-m-d 00:00:00');

        $file = $this->categoryReportDay->generateFile($from, $to);
        if (file_exists($file)) {
            $this->categoryReportDay->sendMail($file, $from, $to);
        } else {
            $this->logger->addWarning(__CLASS__.'>'.__FUNCTION__.': '.__('File %1 not found', $file));
        }
        $this->logger->addInfo("Cronjob CategoryReportLastWeek is executed.");
    }
}
