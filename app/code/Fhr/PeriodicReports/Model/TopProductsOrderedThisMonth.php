<?php


namespace Fhr\PeriodicReports\Model;

use Fhr\Base\Api\CommonInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Intl\DateTimeFactory;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;

/**
 * Top 100 products ordered this month.
 * SKU, Name, Quantity sold, Quantity current in stock.
 * Class TopProductsOrderedThisMonth
 *
 * This report should always be 30 days back from the current date the report is created.
 *
 * @package Fhr\PeriodicReports\Model
 */
class TopProductsOrderedThisMonth
{
    const FILE_PRREFIX = 'fhr_top_products_';
    const XML_PATH_LIMIT = 'periodicreports/topproductsorderedthismonth/limit';
    const XML_PATH_DATE_RANGE_DAYS = 'periodicreports/topproductsorderedthismonth/date_range_days';
    const XML_PATH_EMAILS = 'periodicreports/topproductsorderedthismonth/emails';
    const DEFAULT_DAYS_RANGE = 90;
    const EXT_EXCEL = '.xls';
    const EXT_CSV = '.csv';
    /**
     * @var OrderCollectionFactory
     */
    private $orderCollectionFactory;
    /**
     * @var CommonInterface
     */
    private $common;
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;
    /**
     * @var StockRegistryInterface
     */
    private $stockRegistry;
    /**
     * @var Base
     */
    private $base;
    /**
     * @var DateTimeFactory
     */
    private $dateTimeFactory;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * TopProductsOrderedThisMonth constructor.
     * @param CommonInterface $common
     * @param Base $base
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param ProductRepositoryInterface $productRepository
     * @param DateTimeFactory $dateTimeFactory
     * @param StockRegistryInterface $stockRegistry
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        CommonInterface $common,
        \Fhr\PeriodicReports\Model\Base $base,
        OrderCollectionFactory $orderCollectionFactory,
        ProductRepositoryInterface  $productRepository,
        DateTimeFactory $dateTimeFactory,
        StockRegistryInterface $stockRegistry,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->common = $common;
        $this->base = $base;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->productRepository = $productRepository;
        $this->stockRegistry = $stockRegistry;
        $this->dateTimeFactory = $dateTimeFactory;
        $this->logger = $logger;
    }

    /**
     * Generate Report Data
     * @param null $currentDate
     * @return array
     * @throws NoSuchEntityException
     */
    public function generateData($currentDate = null, $daysRange = 0)
    {
        if (empty($currentDate)) {
            $currentDate = date('Y-m-d H:i:s');
        }
        $dateForDiff = $this->dateTimeFactory->create($currentDate);
        if ($daysRange <= 0) {
            $daysRange = self::DEFAULT_DAYS_RANGE;
        }
        $from = $dateForDiff->modify('-'.$daysRange.' days')->format('Y-m-d H:i:s');
        $to = $currentDate;

        $top100 = [];
        $ordersCollection = $this->orderCollectionFactory->create();
        $ordersCollection->addAttributeToSelect('*')
            ->addAttributeToFilter(
                'created_at',
                [
                    'from' => $from,
                    'to' => $to
                ]
            )
            ->addAttributeToFilter('status', [
                'nin' => [
                    \Magento\Sales\Model\Order::STATE_CANCELED,
                    \Magento\Sales\Model\Order::STATE_HOLDED,
                    \Magento\Sales\Model\Order::STATE_CLOSED
                ]
            ])
            ->setOrder('created_at', 'ASC');
        /** @var \Magento\Sales\Model\Order $order */
        foreach ($ordersCollection as $order) {
            /** @var \Magento\Sales\Api\Data\OrderItemInterface $item */
            foreach ($order->getAllVisibleItems() as $item) {
                try {
                    $product = $this->productRepository->getById($item->getProductId());
                } catch (\Exception $e) {
                    $this->logger->addWarning(
                        __(
                            'TopProductsOrderedThisMonth Product ID: %1 [%2]',
                            $item->getProductId(),
                            $e->getMessage()
                        )
                    );
                }
                if (!empty($top100[$product->getId()])) {
                    $quantitySold = $top100[$product->getId()]['quantitySold'];
                    $quantitySold += $item->getQtyShipped();
                    $top100[$product->getId()]['quantitySold'] = $quantitySold;
                } else {
                    $top100[$product->getId()]['sku'] = $product->getSku();
                    $top100[$product->getId()]['name'] = $product->getName();
                    $top100[$product->getId()]['quantitySold'] = $item->getQtyShipped();
                    $stockItem = $this->stockRegistry->getStockItemBySku($product->getSku());
                    $top100[$product->getId()]['currentQuantityInStock'] = $stockItem->getQty();
                }
            }
        }
        usort($top100, function ($item1, $item2) {
            return $item2['quantitySold'] <=> $item1['quantitySold'];
        });
        $top100 = array_slice($top100, 0, $this->getLimit());
        return $top100;
    }

    /**
     * @param $data
     * @param $month
     * @param null $year
     * @return string
     * @throws \Zend_Date_Exception
     */
    public function generateFile($currentDate = null)
    {
        $data = $this->generateData($currentDate);
        $month = date('m', strtotime($currentDate));
        $year = date('Y', strtotime($currentDate));
        $file = self::FILE_PRREFIX.$this->getLimit().'_'.$this->base->getMonthYearString($month, $year).self::EXT_CSV;
        return $this->base->generateCsvFile(
            ['SKU', 'Name', 'Quantity sold', 'Quantity current in stock'],
            $data,
            $file
        );
    }

    public function generateExcelFile($currentDate = null)
    {
        $sheets = explode(',', $this->getDateRangeDays());
        $dataExcel = [];
        foreach ($sheets as $day) {
            $dataExcel[$day] = $this->generateData($currentDate, $day);
        }
        $month = date('m', strtotime($currentDate));
        $year = date('Y', strtotime($currentDate));
        $file = self::FILE_PRREFIX.$this->getLimit().'_'.$this->base->getMonthYearString($month, $year).self::EXT_EXCEL;
        return $this->base->generateExcelFile(
            ['SKU', 'Name', 'Quantity sold', 'Quantity current in stock'],
            $dataExcel,
            $file
        );
    }

    /**
     * @param $file
     * @param null $currentDate
     * @param bool $deleteAttachFlag
     * @throws \Zend_Date_Exception
     */
    public function sendMail($file, $currentDate = null, $deleteAttachFlag = true)
    {
        $month = date('m', strtotime($currentDate));
        $year = date('Y', strtotime($currentDate));
        $subject = __(
            'Top '.$this->getLimit().' products ordered %1.',
            $this->base->getMonthYearString($month, $year, ' ')
        );
        $message = $subject.__('Check attachment please').'. ';
        $message .= __('Created for date: %1, day intervals: %2', $currentDate, $this->getDateRangeDays());
        $toEmails = $this->common->getConfigValue(self::XML_PATH_EMAILS);
        $this->base->sendMail($file, $subject, $message, $deleteAttachFlag, $toEmails);
    }

    public function getLimit()
    {
        return $this->common->getConfigValue(self::XML_PATH_LIMIT);
    }

    public function getDateRangeDays()
    {
        return $this->common->getConfigValue(self::XML_PATH_DATE_RANGE_DAYS);
    }
}
