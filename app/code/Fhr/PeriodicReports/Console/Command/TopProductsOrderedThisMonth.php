<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\PeriodicReports\Console\Command;

use Fhr\Base\Api\CommonInterface;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Filesystem\Driver\File;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Zend_Date_Exception;

class TopProductsOrderedThisMonth extends Command
{
    const NAME_CURRENT_DATE = "currentDate";
    const NAME_OPTION_DDA = "dont-delete-attach";
    /**
     * @var \Fhr\PeriodicReports\Model\TopProductsOrderedThisMonth
     */
    private $topProductsOrderedThisMonth;
    /**
     * @var File
     */
    private $fileDriver;
    /**
     * @var CommonInterface
     */
    private $common;

    /**
     * TopProductsOrderedThisMonth constructor.
     * @param \Fhr\PeriodicReports\Model\TopProductsOrderedThisMonth $topProductsOrderedThisMonth
     * @param File $fileDriver
     * @param string|null $name
     */
    public function __construct(
        \Fhr\PeriodicReports\Model\TopProductsOrderedThisMonth $topProductsOrderedThisMonth,
        File $fileDriver,
        CommonInterface $common,
        string $name = null
    ) {
        $this->topProductsOrderedThisMonth = $topProductsOrderedThisMonth;
        $this->fileDriver = $fileDriver;
        $this->common = $common;
        parent::__construct($name);
    }

    /**
     * {@inheritdoc}
     * @throws FileSystemException|Zend_Date_Exception
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $currentDate = $input->getArgument(self::NAME_CURRENT_DATE);
        $optionDDA = $input->getOption(self::NAME_OPTION_DDA);

        if (empty($currentDate)) {
            $currentDate = date('Y-m-d H:i:s');
        }
        $file = $this->topProductsOrderedThisMonth->generateExcelFile($currentDate);
        if ($this->fileDriver->isExists($file)) {
            $this->topProductsOrderedThisMonth->sendMail($file, $currentDate, !$optionDDA);
        } else {
            $output->writeln(__('File %1 not found', $file));
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $limit = $this->topProductsOrderedThisMonth->getLimit();
        $this->setName("fhr_periodicreports:topproductsorderedthismonth");
        $this->setDescription("Top $limit products ordered this month.");
        $this->setDefinition([
            new InputArgument(self::NAME_CURRENT_DATE, InputArgument::OPTIONAL, "Date in format Y-m-d H:i:s"),
            new InputOption(self::NAME_OPTION_DDA, "d", InputOption::VALUE_NONE, "Don't delete attach")
        ]);
        parent::configure();
    }
}
