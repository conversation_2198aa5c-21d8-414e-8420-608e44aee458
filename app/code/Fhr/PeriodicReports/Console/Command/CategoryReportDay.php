<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Fhr\PeriodicReports\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class CategoryReportDay extends Command
{
    const FROM = 'from';
    const TO = 'to';

    /**
     * @var \Fhr\PeriodicReports\Model\CategoryReportDay
     */
    private $categoryReportDay;

    /**
     * CategoryReportDay constructor.
     * @param \Fhr\PeriodicReports\Model\CategoryReportDay $categoryReportDay
     * @param string|null $name
     */
    public function __construct(
        \Fhr\PeriodicReports\Model\CategoryReportDay $categoryReportDay,
        string $name = null
    ) {
        $this->categoryReportDay = $categoryReportDay;
        parent::__construct($name);
    }

    /**
     * {@inheritdoc}
     * @throws \Zend_Date_Exception
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $from = $input->getArgument(self::FROM);
        $to = $input->getArgument(self::TO);

        $from = $from.' 00:00:00';
        $to = $to.' 23:59:59';

        $file = $this->categoryReportDay->generateFile($from, $to);
        if (file_exists($file)) {
            $this->categoryReportDay->sendMail($file, $from, $to);
            $output->writeln(__('File %1 generated and e-mail was sent', $file));
        } else {
            $output->writeln(__('File %1 not found', $file));
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("fhr_periodicreports:categoryreportday");
        $this->setDescription("Total sales per main category by date range");
        $this->setDefinition([
            new InputArgument(self::FROM, InputArgument::REQUIRED, "From YYYY-MM-DD"),
            new InputArgument(self::TO, InputArgument::REQUIRED, "To YYYY-MM-DD")
        ]);
        parent::configure();
    }
}
