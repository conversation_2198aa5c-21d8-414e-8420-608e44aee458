<?php
declare(strict_types=1);

namespace Fhr\CustomReports\Model;

use Fhr\CustomReports\Api\Data\CustomreportsInterface;
use Fhr\CustomReports\Api\Data\CustomreportsInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;

class Customreports extends \Magento\Framework\Model\AbstractModel
{

    protected $customreportsDataFactory;

    protected $dataObjectHelper;

    protected $_eventPrefix = 'fhr_customreports';

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param CustomreportsInterfaceFactory $customreportsDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param \Fhr\CustomReports\Model\ResourceModel\Customreports $resource
     * @param \Fhr\CustomReports\Model\ResourceModel\Customreports\Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        CustomreportsInterfaceFactory $customreportsDataFactory,
        DataObjectHelper $dataObjectHelper,
        \Fhr\CustomReports\Model\ResourceModel\Customreports $resource,
        \Fhr\CustomReports\Model\ResourceModel\Customreports\Collection $resourceCollection,
        array $data = []
    ) {
        $this->customreportsDataFactory = $customreportsDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve customreports model with customreports data
     * @return CustomreportsInterface
     */
    public function getDataModel()
    {
        $customreportsData = $this->getData();

        $customreportsDataObject = $this->customreportsDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $customreportsDataObject,
            $customreportsData,
            CustomreportsInterface::class
        );

        return $customreportsDataObject;
    }
}
