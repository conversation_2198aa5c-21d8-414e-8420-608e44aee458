<?php
declare(strict_types=1);

namespace Fhr\CustomReports\Model;

use Fhr\CustomReports\Api\Data\PaymentMethodInterface;
use Fhr\CustomReports\Api\Data\PaymentMethodSearchResultsInterface;
use Fhr\CustomReports\Api\Data\PaymentMethodSearchResultsInterfaceFactory;
use Fhr\CustomReports\Api\PaymentMethodRepositoryInterface;
use Fhr\CustomReports\Model\ResourceModel\PaymentMethod as ResourcePaymentMethod;
use Fhr\CustomReports\Model\ResourceModel\PaymentMethod\CollectionFactory as PaymentMethodCollectionFactory;
use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class PaymentMethodRepository implements PaymentMethodRepositoryInterface
{
    /**
     * @var ResourcePaymentMethod
     */
    private $resource;
    /**
     * @var PaymentMethodFactory
     */
    private $PaymentMethodFactory;
    /**
     * @var PaymentMethodCollectionFactory
     */
    private $PaymentMethodCollectionFactory;
    /**
     * @var PaymentMethodSearchResultsInterfaceFactory
     */
    private $searchResultsFactory;
    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;
    /**
     * @var JoinProcessorInterface
     */
    private $extensionAttributesJoinProcessor;
    /**
     * @var ExtensibleDataObjectConverter
     */
    private $extensibleDataObjectConverter;

    /**
     * @param ResourcePaymentMethod $resource
     * @param PaymentMethodFactory $PaymentMethodFactory
     * @param PaymentMethodCollectionFactory $PaymentMethodCollectionFactory
     * @param PaymentMethodSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     * @param JoinProcessorInterface $extensionAttributesJoinProcessor
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     */
    public function __construct(
        ResourcePaymentMethod $resource,
        PaymentMethodFactory $PaymentMethodFactory,
        PaymentMethodCollectionFactory $PaymentMethodCollectionFactory,
        PaymentMethodSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor,
        JoinProcessorInterface $extensionAttributesJoinProcessor,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter
    ) {
        $this->resource = $resource;
        $this->PaymentMethodFactory = $PaymentMethodFactory;
        $this->PaymentMethodCollectionFactory = $PaymentMethodCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->extensionAttributesJoinProcessor = $extensionAttributesJoinProcessor;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
    }

    /**
     * {@inheritdoc}
     */
    public function save(
        $paymentMethod
    ) {
        $PaymentMethodData = $this->extensibleDataObjectConverter->toNestedArray(
            $paymentMethod,
            [],
            PaymentMethodInterface::class
        );

        $PaymentMethodModel = $this->PaymentMethodFactory->create()->setData($PaymentMethodData);

        try {
            $this->resource->save($PaymentMethodModel);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the PaymentMethod: %1',
                $exception->getMessage()
            ));
        }
        return $PaymentMethodModel->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function get($id)
    {
        $PaymentMethod = $this->PaymentMethodFactory->create();
        $this->resource->load($PaymentMethod, $id);
        if (!$PaymentMethod->getId()) {
            throw new NoSuchEntityException(__('PaymentMethod with id "%1" does not exist.', $id));
        }
        return $PaymentMethod->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getList($searchCriteria)
    {
        $collection = $this->PaymentMethodCollectionFactory->create();

        $this->extensionAttributesJoinProcessor->process(
            $collection,
            PaymentMethodInterface::class
        );

        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model->getDataModel();
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * {@inheritdoc}
     */
    public function delete($paymentMethod)
    {
        try {
            $PaymentMethodModel = $this->PaymentMethodFactory->create();
            $this->resource->load($PaymentMethodModel, $paymentMethod->getId());
            $this->resource->delete($PaymentMethodModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the PaymentMethod: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteById($paymentMethodId)
    {
        return $this->delete($this->get($paymentMethodId));
    }
}
