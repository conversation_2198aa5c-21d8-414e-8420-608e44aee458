<?php

namespace Fhr\CustomReports\Controller\Adminhtml\OrdersystemCost;

use Fhr\CustomReports\Model\Reports\OrdersystemCost;
use Magento\Backend\App\Action;

class Save extends Action
{
    const ADMIN_RESOURCE = 'Fhr_CustomReports::fhr_customreports_ordersystem_cost';

    /**
     * @var OrdersystemCost
     */
    private $ordersystemCost;

    /**
     * Save constructor.
     * @param Action\Context $context
     * @param OrdersystemCost $ordersystemCost
     */
    public function __construct(
        Action\Context $context,
        OrdersystemCost $ordersystemCost
    ) {
        parent::__construct($context);
        $this->ordersystemCost = $ordersystemCost;
    }

    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $this->ordersystemCost->getData();
        $this->messageManager->addSuccessMessage(__('Report created'));
        return $resultRedirect->setPath('*/*/');
    }
}
