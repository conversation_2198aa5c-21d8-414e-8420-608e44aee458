<?php

namespace Fhr\CustomReports\Console\Command;

use Symfony\Component\Console\Command\Command;
use Fhr\CustomReports\Model\Reports\PaymentMethod as ReportsPaymentMethod;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class PaymentMethod extends Command
{
    const FROM_DATE = 'from';
    const TO_DATE = 'to';
    const PAYMENT_METHODS = 'payment_methods';

    /**
     * @var ReportsPaymentMethod
     */
    private $paymentMethod;

    /**
     * PaymentMethod constructor.
     * @param ReportsPaymentMethod $paymentMethod
     * @param string|null $name
     */
    public function __construct(
        ReportsPaymentMethod $paymentMethod,
        string $name = null
    ) {
        parent::__construct($name);
        $this->paymentMethod = $paymentMethod;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $from = $input->getArgument(self::FROM_DATE). ' 00:00:00';
        $to = $input->getArgument(self::TO_DATE).' 23:59:59';
        $method = explode(',', $input->getArgument(self::PAYMENT_METHODS));
        if (empty($method)) {
            $method = \Fhr\CustomReports\Model\Reports\PaymentMethod::DEFAULT_PAYMENT_METHODS;
        }
        $filename = $this->paymentMethod->createReportCsv($from, $to, $method);
        $output->writeln(__('Report Payment Method %1', $filename));
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("fhr_customreports:paymentmethod");
        $this->setDescription("Generate Stock List report");
        $this->setDefinition([
            new InputArgument(self::FROM_DATE, InputArgument::REQUIRED, "From date format YYYY-MM-DD"),
            new InputArgument(self::TO_DATE, InputArgument::REQUIRED, "To date format YYYY-MM-DD"),
            new InputArgument(self::PAYMENT_METHODS, InputArgument::OPTIONAL, "Payment methods codes separated by comma(,)"),
        ]);
        parent::configure();
    }
}
