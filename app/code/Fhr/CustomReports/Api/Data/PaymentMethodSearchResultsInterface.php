<?php

namespace Fhr\CustomReports\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

interface PaymentMethodSearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get OrderItems list.
     * @return PaymentMethodInterface[]
     */
    public function getItems(): array;

    /**
     * Set order_increment_id list.
     * @param PaymentMethodInterface[] $items
     * @return $this
     */
    public function setItems($items);
}
