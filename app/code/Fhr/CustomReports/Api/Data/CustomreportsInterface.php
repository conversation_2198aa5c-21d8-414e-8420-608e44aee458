<?php
declare(strict_types=1);

namespace Fhr\CustomReports\Api\Data;

interface CustomreportsInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{

    const CUSTOMREPORTS_ID = 'id';
    const REPORT_IDENTIFIER = 'report_identifier';
    const TIMESTAMP = 'timestamp';
    const METADATA = 'metadata';

    /**
     * Get customreports_id
     * @return string|null
     */
    public function getCustomreportsId();

    /**
     * Set customreports_id
     * @param string $customreportsId
     * @return \Fhr\CustomReports\Api\Data\CustomreportsInterface
     */
    public function setCustomreportsId($customreportsId);

    /**
     * Get report_identifier
     * @return string|null
     */
    public function getReportIdentifier();

    /**
     * Set report_identifier
     * @param string $reportIdentifier
     * @return \Fhr\CustomReports\Api\Data\CustomreportsInterface
     */
    public function setReportIdentifier($reportIdentifier);

    /**
     * Get timestamp
     * @return string|null
     */
    public function getTimestamp();

    /**
     * Set timestamp
     * @param string $timestamp
     * @return \Fhr\CustomReports\Api\Data\CustomreportsInterface
     */
    public function setTimestamp($timestamp);

    /**
     * Get metadata
     * @return string|null
     */
    public function getMetadata();

    /**
     * Set metadata
     * @param string $metadata
     * @return \Fhr\CustomReports\Api\Data\CustomreportsInterface
     */
    public function setMetadata($metadata);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Fhr\CustomReports\Api\Data\CustomreportsExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Fhr\CustomReports\Api\Data\CustomreportsExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Fhr\CustomReports\Api\Data\CustomreportsExtensionInterface $extensionAttributes
    );
}
