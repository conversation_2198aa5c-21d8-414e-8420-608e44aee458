<?php

namespace Fhr\NetsNetaxeptPayment\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class OrderPlaceAfter implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        try {
            $order = $observer->getEvent()->getOrder();
            if ($order->getPayment()->getMethod() == \Fhr\NetsNetaxeptPayment\Model\Netsnetaxept::PAYMENT_CODE) {
                $orderState = \Magento\Sales\Model\Order::STATE_PENDING_PAYMENT;
                $order->setState($orderState)->setStatus(\Magento\Sales\Model\Order::STATE_PENDING_PAYMENT);
                $order->save();
            }
        } catch (\Exception $e) {
            $writer = new \Zend\Log\Writer\Stream(BP . '/var/log/NetsNetaxeptPaymentException.log');
            $logger = new \Zend\Log\Logger();
            $logger->addWriter($writer);
            $logger->info($e->getMessage());
        }
    }
}
