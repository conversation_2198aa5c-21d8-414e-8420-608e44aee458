<?php
/**
 *
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Fhr\Catalog\Api\Data;

/**
 * @api
 * @since 100.0.2
 */
interface ProductInterface extends \Magento\Catalog\Api\Data\ProductInterface
{
    /**
     * Product qty
     *
     * @return string
     */
    public function getQty();

    /**
     * Set product qty
     *
     * @param string $qty
     * @return $this
     */
    public function setQty($qty);

    /**
     * Product qty
     *
     * @return string
     */
    public function getFhrSfCategoryPathToTag();

    /**
     * Set product qty
     *
     * @param string $fhr_sf_category_path_to_tag
     * @return $this
     */
    public function setFhrSfCategoryPathToTag($fhr_sf_category_path_to_tag);

    /**
     * Product qty
     *
     * @return string
     */
    public function getRealUrl();
}
