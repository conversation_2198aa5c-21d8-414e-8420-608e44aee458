<?php

namespace Fhr\Catalog\Helper;

use <PERSON><PERSON>o\Catalog\Helper\Output;
use Magento\Catalog\Model\Product;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\ProductAlert\Helper\Data as ProductAlertHelper;
use Magento\ProductAlert\Model\ResourceModel\Stock\CollectionFactory;

class Delivery extends \Magento\Framework\App\Helper\AbstractHelper
{
    const COLOR_GREEN = '#66bf48';
    const COLOR_YELLOW = '#f5ca59';
    const COLOR_RED = '#ba0001';
    const COLOR_GREY = '#d2d2d2';
    const TYPE_PRODUCTALERT_STOCK = 'stock';
    const NO_ETA_CATEGORY_IDS = ['1092', '1099', '1138', '1100', '1142', '1101', '1143', '1102', '1141', '1126', '1144', '1127', '1145', '1128', '1140', '1129', '1139', '1094', '1103', '1146', '1104', '1151', '1105', '1153', '1106', '1154', '1107', '1155', '1108', '1156', '1109', '1157', '1110', '1158', '1111', '1159', '1112', '1160', '1113', '1161', '1114', '1162', '1115', '1163', '1116', '1165', '1130', '1150', '1131', '1149', '1132', '1148', '1133', '1147', '1134', '1164', '1135', '1152', '1324', '1326', '1374', '1390', '1095', '1117', '1167', '1118', '1168', '1119', '1169', '1120', '1170', '1121', '1171', '1122', '1172', '1123', '1173', '1124', '1174', '1125', '1175', '1136', '1166', '1325', '1375', '1391', '11376', '1366', '1367'];

    /**
     * @var Output
     */
    private $catalogHelperOutput;
    /**
     * @var StockRegistryInterface
     */
    private $stockRegistry;
    /**
     * @var ProductAlertHelper
     */
    private $productAlertHelper;
    /**
     * @var Session
     */
    private $customerSession;
    /**
     * @var CollectionFactory
     */
    private $productAlertStockCollectionFactory;
    /**
     * @var \Magento\ProductAlert\Block\Email\Stock
     */
    private $productAlertBlockEmailStock;
    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * Delivery constructor.
     * @param Context $context
     * @param Output $catalogHelperOutput
     * @param StockRegistryInterface $stockRegistry
     * @param ProductAlertHelper $productAlertHelper
     * @param Session $customerSession
     * @param CollectionFactory $productAlertStockCollectionFactory
     * @param \Magento\ProductAlert\Block\Email\Stock $productAlertBlockEmailStock
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     */
    public function __construct(
        Context $context,
        Output $catalogHelperOutput,
        StockRegistryInterface $stockRegistry,
        ProductAlertHelper $productAlertHelper,
        Session $customerSession,
        CollectionFactory $productAlertStockCollectionFactory,
        \Magento\ProductAlert\Block\Email\Stock $productAlertBlockEmailStock,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
    ) {
        $this->catalogHelperOutput = $catalogHelperOutput;
        $this->stockRegistry = $stockRegistry;
        $this->productAlertHelper = $productAlertHelper;
        $this->customerSession = $customerSession;
        $this->productAlertStockCollectionFactory = $productAlertStockCollectionFactory;
        $this->productAlertBlockEmailStock = $productAlertBlockEmailStock;
        $this->productRepository = $productRepository;
        parent::__construct($context);
    }

    /**
     * @param Product $product
     * @return string
     * @throws LocalizedException
     */
    public function getDeliveryTerm($product)
    {
        $default = __('7 days +');
        if (!empty($product)) {
            $product = $this->productRepository->get($product->getSku());
            $productDelivery = $product->getDeliveryTerms();
            $productShipping = $product->getShippingAttribute();
            $stockItem = $this->stockRegistry->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
            $qty = (int)$stockItem->getQty();
            // NO ETA for out of stock mackbooks
            if (($qty < 1) && !empty(array_intersect($product->getCategoryIds(), self::NO_ETA_CATEGORY_IDS))) {
                return __('NO ETA');
            }
            // If product in stock then 1-2 days
            if ($qty > 0) {
                return __('1-2 days');
            }
            // 'Shipping (day)' has more priority then 'Delivery terms'
            if (!empty($productShipping)) {
                return __($productShipping);
            }
            if (!empty($productDelivery)) {
                return $product->getResource()->getAttribute('delivery_terms')->getFrontend()->getValue($product);
            }
            return $default;
        }
        return $default;
    }

    /**
     * @param Product $product
     * @return array
     */
    public function getQty($product)
    {
        $return = [];
        if (!empty($product)) {
            $stockItem = $this->stockRegistry->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
            $qty = (int)$stockItem->getQty();
            $bars = 5;
            $color = self::COLOR_GREY;
            $qty_html = '';

            if ($qty > 100) {
                $qty = floor($qty / 50) * 50;
                $qty_html = ($qty == 0) ? '' : $qty . '+ ';
                $actual = 5;
                $color = self::COLOR_GREEN;
            } elseif (($qty > 50) && ($qty <= 100)) {
                $qty = floor($qty / 50) * 50;
                $qty_html = ($qty == 0) ? '' : $qty . '+ ';
                $actual = 4;
                $color = self::COLOR_YELLOW;
            } elseif ((($qty >= 20) && ($qty <= 50))) {
                $qty_html = ($qty == 0) ? '' : $qty . '+ ';
                $actual = 3;
                $color = self::COLOR_YELLOW;
            } elseif ((($qty > 5) && ($qty < 20))) {
                $qty = floor($qty / 5) * 5;
                $qty_html = ($qty == 0) ? '' : $qty . '+ ';
                $actual = 2;
                $color = self::COLOR_RED;
            } elseif ((($qty > 0) && ($qty <= 5))) {
                $actual = 1;
                $color = self::COLOR_RED;
            } else {
                $actual = 0;
            }
            $return = [
                'qty' => (int)$stockItem->getQty(),
                'qty_html' => $qty_html,
                'bars' => $bars,
                'actual' => $actual,
                'color' => $color
            ];
        }
        return $return;
    }

    /**
     * @param $product
     * @return array
     */
    public function getQtyHyva($product): array
    {
        // Default grey
        $return = [
            'color' => 'bg-cgrey',
            'qty' => 0,
            'txt' => ''
        ];
        if (!empty($product)) {
            $stockItem = $this->stockRegistry->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
            $qty = (int)$stockItem->getQty();
            if ($qty < 0) $qty = 0;
            $color = 'bg-cgrey';
            $txt = '';
            if ($qty <= 0) { // Red 0
                $color = 'bg-cred';
                $txt = $qty;
            }
            if ($qty > 0 ) { // Yellow 0-5
                $color = 'bg-cyellow';
                $txt = $qty;
            }
            if ($qty > 5) { // Green 6+
                $color = 'bg-cgreen';
                $txt = $qty;
                if ($qty > 100) $txt = '100+';
            }
            $return = [
                'qty' => (int)$stockItem->getQty(),
                'color' => $color,
                'txt' => $txt
            ];
        }
        return $return;
    }

    /**
     * @param Product $product
     * @return string
     */
    public function getQtyToHtml($product)
    {
        $html = '';
        $data = $this->getQty($product);
        $websiteId = $product->getStore()->getWebsiteId();
        $qty = $data['qty'];
        $qty_html = $data['qty_html'];
        $actual = $data['actual'];
        $color = $data['color'];
        $bars = $data['bars'];
        $displayrealstockqty = $this->scopeConfig->getValue(
            'fhrbase/frontcontrols/displayrealstockqty',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        //
        if (($qty < 1) && ($websiteId == 2)) {
            $html .= '<span class="warning-outofstoc$qty_htmlk">'.__("Out of Stock").'</span>';
        } else {
            $html .= '<ul class="fa-ul" style=" margin-bottom: -10px; margin-left: 1.5em;">';
            for ($i = 0; $i < $actual; $i++) {
                $html .= '<li style="float: left; top: -.3em; font-size: 9px; margin-right: 8px; color: '.$color.';">';
                $html .= '<i class="fa-li fa fa-square"></i>&nbsp;';
                $html .= '</li>';
            }
            for ($i = 0; $i < ($bars - $actual); $i++) {
                $html .= '<li style="float: left; top: -.3em; font-size: 9px; margin-right: 8px; color: '
                    .self::COLOR_GREY.';">';
                $html .= '<i class="fa-li fa fa-square"></i>&nbsp;';
                $html .= '</li>';
            }
            $html .= '</ul>';
        }
        //
        if (!$displayrealstockqty) {
            $html .= $qty_html;
        } else {
            $html .= ($qty > 0)?(int) $qty:'';
        }
        return $html;
    }

    /**
     * @param Product $product
     * @return string
     */
    public function getProductAlertSubscriptionHtml($product)
    {
        $html = '';
        if ($this->productAlertHelper->isStockAlertAllowed()) {
            if ($this->customerSession->isLoggedIn()) {
                $stockItem = $this->stockRegistry
                    ->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
                $qty = (int)$stockItem->getQty();
                if ($qty < 1) {
                    $this->productAlertHelper->setProduct($product);
                    /** @var \Magento\ProductAlert\Model\ResourceModel\Stock\Collection $collection */
                    $collection = $this->productAlertStockCollectionFactory->create();
                    $collection
                        ->addFieldToFilter('product_id', ['eq' => $product->getId()])
                        ->addFieldToFilter('customer_id', ['eq' => $this->customerSession->getCustomerId()]);
                    if ($collection->count() > 0) {
                        $href = $this->productAlertBlockEmailStock->getProductUnsubscribeUrl($product->getId());
                        $title = __('Unsubscribe');
                        $html = '<a class="fhr-product-subscribe unsubscribe" href="'
                            . $href
                            . '" title="' . $title
                            . '"></a>';
                    } else {
                        $href = $this->productAlertHelper->getSaveUrl(self::TYPE_PRODUCTALERT_STOCK);
                        $title = __('Sign up to get notified when this product is back in stock');
                        $html = '<a class="fhr-product-subscribe" href="'
                            . $href . '" title="'
                            . $title
                            . '"></a>';
                    }
                }
            }
        }

        return $html;
    }
}
