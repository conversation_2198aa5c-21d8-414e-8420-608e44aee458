<?php

namespace Fhr\Sales\Rewrite\Magento\Sales\Block\Order;

class Info extends \Magento\Sales\Block\Order\Info
{
    /**
     * @return void
     */
    protected function _prepareLayout()
    {
        $this->pageConfig->getTitle()->set(__(
            'Order #%1&nbsp;[%2]',
            $this->getOrder()->getRealOrderId(),
            $this->getOrder()->getId(),
        ));
        $infoBlock = $this->paymentHelper->getInfoBlock($this->getOrder()->getPayment(), $this->getLayout());
        $this->setChild('payment_info', $infoBlock);
    }
}
