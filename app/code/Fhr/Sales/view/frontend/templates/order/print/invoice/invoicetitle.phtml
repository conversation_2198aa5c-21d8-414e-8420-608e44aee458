<?php
/** @var \Fhr\Sales\Block\Order\PrintOrder\InvoiceTitle $block */
/** @var \Magento\Sales\Api\Data\OrderInterface $order */
$order = $block->getCurrentOrder();
/** @var \Magento\Sales\Api\Data\InvoiceInterface $invoice */
$invoice = $block->getCurrentInvoice();
?>
<table id="fhr-sales-order-print-invoice-title">
    <tbody>
    <tr>
        <td class="fhr-logo"><?= $block->getLayout()->createBlock('Magento\Theme\Block\Html\Header\Logo')
                ->setTemplate('Magento_Theme::html/header/logo.phtml')->toHtml(); ?></td>
        <td></td>
        <td class="fhr-contacts">
            <p>
                <span><?= __('Tel') ?>.:&nbsp;<?= $block->getConfigValue($block::SITE_PHONE) ?></span><br>
                <span><?= __('Homepage') ?>:&nbsp;<?= $block->getConfigValue($block::SITE_URL) ?></span><br>
                <span><?= __('E-Mail') ?>:&nbsp;<?= $block->getConfigValue($block::SITE_EMAIL) ?></span><br>
            </p>
        </td>
    </tr>
    <tr>
        <td>
            <p>
                <span class="txt-small"><?=$block->getConfigValue($block::STORE_NAME)?>,
                    <?=$block->getConfigValue($block::STORE_STREET_LINE1)?>,
                    <?=$block->getConfigValue($block::STORE_POSTCODE)?> <?=$block->getConfigValue($block::STORE_CITY)?>,
                <?=$block->getCountryname($block->getConfigValue($block::STORE_COUNTRY_ID))?></span><br>
                <span><?=__('Firma')?></span><br>
                <span><?=$order->getBillingAddress()->getCompany()?></span><br>
                <span><?=implode(',', $order->getBillingAddress()->getStreet())?></span><br>
                <span><?=$order->getBillingAddress()->getPostcode()?>&nbsp;<?=$order->getBillingAddress()->getCity()?></span><br>
                <span><?=$block->getCountryname($order->getBillingAddress()->getCountryId())?></span><br>
                <span><?=__('organisation number')?>:</span>
                <span><?=$block->getCompanyId($order->getCustomerId());?></span>
            </p>
        </td>
        <td></td>
        <td>
            <p>
                <span>Invoice:</span>&nbsp;<span><?=$invoice->getEntityId()?></span>
                <span>Operation number:</span>&nbsp;<span>#<?=$invoice->getIncrementId()?></span><br>
                <span>Date:</span>&nbsp;<span><?=date('d.m.Y', strtotime($order->getCreatedAt()))?></span><br>
                <span>Customer number:</span>&nbsp;<span><?=$order->getCustomerId()?></span><br>
            </p>
            <p class="txt-small">Please specify when making inquiries!</p>
        </td>
    </tr>
    <tr>
        <td>
            <p>
                <span><?=__('Delivery service')?>:</span>&nbsp;<span><?=$order->getShippingDescription()?></span><br>
                <span><?=__('Delivery specs')?>:</span>&nbsp;<span><?=__('invoice')?></span><br>
                <span><?=__('Date of delivery')?>:</span>&nbsp;<span>&nbsp;</span><br>
                <span><?=__('Trackingnumbers')?>:</span>&nbsp;<span><?=$block->getTrackNumbers()?></span>
            </p>
        </td>
        <td>
            <p>
                <span><?=__('Reference')?>:</span>&nbsp;<span>&nbsp;</span><br>
                <span><?=__('Your sign')?>:</span>&nbsp;<span><?=$order->getBillingAddress()->getFirstname()
                    .' '.$order->getBillingAddress()->getLastname()?></span>
            </p>
        </td>
        <td>
            <p>
                <span><?=__('VAT number')?>:</span>&nbsp;<span><?=$order->getCustomerTaxvat()?></span><br>
                <span><?=__('Our tax number')?>:</span>&nbsp;<span><?=$block->getConfigValue($block::STORE_VAT)?></span>
            </p>
        </td>
    </tr>
    </tbody>
</table>
