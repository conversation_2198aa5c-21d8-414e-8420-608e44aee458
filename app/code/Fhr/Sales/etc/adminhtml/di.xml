<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\Reporting">
        <plugin name="sales_grid_collection" type="Fhr\Sales\Plugin\Sales\Order\Grid"/>
    </type>
    <type name="Magento\Sales\Block\Adminhtml\Order\View">
        <plugin name="fhr_sales_plugin-btn-order-compleat" type="Fhr\Sales\Plugin\PluginBtnOrderCompleat"
                disabled="false" sortOrder="20"/>
        <plugin name="fhr_sales_plugin-btn-order-fhrcancel" type="Fhr\Sales\Plugin\PluginBtnOrderFhrCancel"
                disabled="false" sortOrder="30"/>
    </type>
    <preference for="Magento\Sales\Model\AdminOrder\Create"
                type="Fhr\Sales\Rewrite\Magento\Sales\Model\AdminOrder\Create"/>
    <!--    <preference for="Magento\Sales\Model\ResourceModel\Report\Order\Createdat"-->
    <!--                type="Fhr\Sales\Model\ResourceModel\Report\Order\Createdat"/>-->
    <!--    <preference for="Magento\Sales\Model\ResourceModel\Report\Order\Updatedat"-->
    <!--                type="Fhr\Sales\Model\ResourceModel\Report\Order\Updatedat"/>-->
    <!-- Adds additional data to the orders grid collection -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <plugin name="fhr_sales_orders_grid_add_data_to_orders_grid"
                type="Fhr\Sales\Plugin\AddDataToOrdersGrid"
                sortOrder="10"
                disabled="false"/>
    </type>
    <preference
            for="Magento\Sales\Model\ResourceModel\Order\Grid\Collection"
            type="Fhr\Sales\Model\ResourceModel\Order\Grid\Collection"/>

    <type name="Magento\Sales\Block\Adminhtml\Order\Create\Items\Grid">
        <plugin name="Fhr_Sales_Plugin_Backend_Magento_Sales_Block_Adminhtml_Order_Create_Items_Grid"
                type="Fhr\Sales\Plugin\Backend\Magento\Sales\Block\Adminhtml\Order\Create\Items\Grid" sortOrder="10"
                disabled="true"/>
    </type>
    <preference for="Magento\Sales\Controller\Adminhtml\Order\View"
        type="Fhr\Sales\Rewrite\Controller\Adminhtml\Order\View" />
</config>
