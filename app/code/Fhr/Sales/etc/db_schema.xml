<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="fhr_sales_order_ordersellersmanager_userid" resource="default"
           engine="innodb" comment="FHR ordersellersmanager userid">
        <column name="id" xsi:type="int" padding="10" unsigned="true"
                nullable="false" identity="true" comment="Entity Id"/>
        <column name="order_id" xsi:type="int" padding="10" unsigned="true" nullable="false" identity="false"
                comment="Order Id"/>
        <column name="fhr_ordersellersmanager_userid" xsi:type="int" padding="10" unsigned="false" nullable="false"
                identity="false" default="0" comment="fhr ordersellersmanager userid"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="FHR_SALES_ORDERSELLERSMANAGER_ORDER_ID">
            <column name="order_id"/>
        </constraint>
        <index referenceId="FHR_SALES_ORDERSELLERSMANAGER_USERID" indexType="btree">
            <column name="fhr_ordersellersmanager_userid"/>
        </index>
    </table>
<!--    <table name="sales_order_aggregated_created">-->
<!--        <column xsi:type="int" name="fhr_ordersellersmanager_userid" padding="11" unsigned="false" nullable="false"-->
<!--                identity="false"-->
<!--                default="0" comment="Seller manager user ID"/>-->
<!--        <index referenceId="SALES_ORDER_AGGREGATED_CREATED_FHR_ORDERSELLERSMANAGER_USERID" indexType="btree">-->
<!--            <column name="fhr_ordersellersmanager_userid"/>-->
<!--        </index>-->
<!--        <constraint xsi:type="unique" referenceId="SALES_ORDER_AGGREGATED_CREATED_PERIOD_STORE_ID_ORDER_STATUS">-->
<!--            <column name="period"/>-->
<!--            <column name="store_id"/>-->
<!--            <column name="order_status"/>-->
<!--            <column name="fhr_ordersellersmanager_userid"/>-->
<!--        </constraint>-->
<!--    </table>-->
<!--    <table name="sales_order_aggregated_updated">-->
<!--        <column xsi:type="int" name="fhr_ordersellersmanager_userid" padding="11" unsigned="false" nullable="false"-->
<!--                identity="false"-->
<!--                default="0" comment="Seller manager user ID"/>-->
<!--        <index referenceId="SALES_ORDER_AGGREGATED_CREATED_FHR_ORDERSELLERSMANAGER_USERID" indexType="btree">-->
<!--            <column name="fhr_ordersellersmanager_userid"/>-->
<!--        </index>-->
<!--        <constraint xsi:type="unique" referenceId="SALES_ORDER_AGGREGATED_UPDATED_PERIOD_STORE_ID_ORDER_STATUS">-->
<!--            <column name="period"/>-->
<!--            <column name="store_id"/>-->
<!--            <column name="order_status"/>-->
<!--            <column name="fhr_ordersellersmanager_userid"/>-->
<!--        </constraint>-->
<!--    </table>-->
</schema>
