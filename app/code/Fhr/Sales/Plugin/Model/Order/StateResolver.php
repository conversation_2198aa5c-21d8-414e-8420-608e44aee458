<?php

namespace Fhr\Sales\Plugin\Model\Order;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\Order\StateResolver as OrderStateResolver;
use Magento\Framework\Session\SessionManagerInterface;

class StateResolver
{
    /**
     * @var SessionManagerInterface
     */
    private $session;

    public function __construct(
        SessionManagerInterface $session
    ) {
        $this->session = $session;
    }

    /**
     * @param OrderStateResolver $subject
     * @param OrderInterface $order
     * @param array $arguments
     * @return array
     */
    public function beforeGetStateForOrder(
        OrderStateResolver $subject,
        OrderInterface $order,
        array $arguments = []
    ) {
        $this->session->start();
        $this->session->setFhrShippingState($order->getStatus());
        return [$order, $arguments];
    }
}
