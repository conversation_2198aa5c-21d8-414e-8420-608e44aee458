<?php
/**
 * By tutorial:
 * https://belvg.com/blog/how-to-add-column-in-sales-order-grid-in-magento.html
 * https://github.com/Galillei/sample_add_columns_to_order_grid/tree/2.3/Belvg/ColumnToOrderGrid
 */
namespace Fhr\Sales\Plugin\Sales\Order;

use Magento\Framework\Api\Search\SearchResultInterface;
use Magento\Framework\View\Element\UiComponent\DataProvider\Reporting;

class Grid
{
    public static $table = 'sales_order_grid';
    public static $leftJoinTable = 'fhr_sales_order_ordersellersmanager_userid';
    /**
     * @var \Magento\Eav\Api\AttributeRepositoryInterface
     */
    private $attributeRepository;

    /**
     * @param \Magento\Eav\Api\AttributeRepositoryInterface $attributeRepository
     */
    public function __construct(
        \Magento\Eav\Api\AttributeRepositoryInterface $attributeRepository
    ) {
        $this->attributeRepository = $attributeRepository;
    }

    /**
     * @param Reporting $intercepter
     * @param SearchResultInterface $collection
     * @return SearchResultInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function afterSearch($intercepter, $collection)
    {
        if ($collection->getMainTable() === $collection->getConnection()->getTableName(self::$table)) {
            $leftJoinTableName = $collection->getConnection()->getTableName(self::$leftJoinTable);
            $collection->getSelect()->joinLeft(
                ['fhrsoou' => $leftJoinTableName],
                'fhrsoou.order_id=main_table.entity_id',
                ['fhr_ordersellersmanager_userid' => 'fhrsoou.fhr_ordersellersmanager_userid']
            );

            $attribute = $this->attributeRepository->get(
                \Magento\Customer\Model\Customer::ENTITY,
                \Fhr\Ipiccolo\Setup\Patch\Data\CompanyCustomerAttr::CODE
            );
            $attributeId = $attribute->getAttributeId();
            $customerEntityVarcharTableName = $collection->getResource()->getTable('customer_entity_varchar');
            $collection->getSelect()->joinLeft(
                ['cev' => $customerEntityVarcharTableName],
                'cev.entity_id = main_table.customer_id AND cev.attribute_id = '.$attributeId,
                ['company' => 'cev.value']
            );
        }
        return $collection;
    }
}
