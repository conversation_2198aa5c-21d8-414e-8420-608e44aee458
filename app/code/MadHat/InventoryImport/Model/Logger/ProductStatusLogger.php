<?php

namespace MadHat\InventoryImport\Model\Logger;

class ProductStatusLogger extends \Monolog\Logger
{
    public function __construct(
        $name = "import_inventory_status",
        array $handlers = [],
        array $processors = []
    ) {
        parent::__construct($name, $handlers, $processors);
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return 'Inventory Status Import';
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return 'success';
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return 'log/import/inventory_status_import_' . date("Ymd") . '.log';
    }

    /**
     * @return string
     */
    public function getStartTime(): string
    {
        return date('Y-m-d H:i:s');
    }

}
