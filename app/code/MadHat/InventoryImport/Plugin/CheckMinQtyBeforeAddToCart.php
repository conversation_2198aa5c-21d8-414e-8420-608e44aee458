<?php

namespace MadHat\InventoryImport\Plugin;

use Magento\Checkout\Model\Cart;
use Magento\Framework\Exception\LocalizedException;
use MadHat\InventoryImport\Helper\ProductStatusConfig;
use Magento\Framework\Math\Division as MathDivision;

class CheckMinQtyBeforeAddToCart
{
    /**
     * @var ProductStatusConfig
     */
    public $productStatusConfig;

    /**
     * @var MathDivision
     */
    public $mathDivision;

    public function __construct(
        MathDivision $mathDivision,
        ProductStatusConfig $productStatusConfig
    ) {
        $this->mathDivision = $mathDivision;
        $this->productStatusConfig = $productStatusConfig;
    }

    /**
     * @throws LocalizedException
     */
    public function beforeAddProduct(Cart $subject, $productInfo, $requestInfo = null)
    {
        $qty = isset($requestInfo['qty']) ? $requestInfo['qty'] : 1;
        if ($this->productStatusConfig->isMinQtyEnable()) {
            $qtyIncrements = $this->productStatusConfig->getMinSaleConfigQty();
            if ($qtyIncrements && $this->mathDivision->getExactDivision($qty, $qtyIncrements) != 0) {
                throw new LocalizedException(
                    __(
                        'You can buy this product only in quantities of %1 at a time.',
                        $qtyIncrements
                    )
                );
            }
        }

        return [$productInfo, $requestInfo];
    }
}
