<?php

namespace MadHat\InventoryImport\Plugin;

use Magento\Checkout\Model\Sidebar;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Math\Division as MathDivision;
use MadHat\InventoryImport\Helper\ProductStatusConfig;

class CheckMinQtyBeforeUpdateQuoteItem
{
    /**
     * @var ProductStatusConfig
     */
    public $productStatusConfig;

    /**
     * @var MathDivision
     */
    protected $mathDivision;

    public function __construct(
        MathDivision $mathDivision,
        ProductStatusConfig $productStatusConfig
    ) {
        $this->mathDivision = $mathDivision;
        $this->productStatusConfig = $productStatusConfig;
    }

    /**
     * @throws LocalizedException
     */
    public function beforeUpdateQuoteItem(Sidebar $subject, $itemId, $itemQty)
    {
        $qty = $itemQty;

        if ($this->productStatusConfig->isMinQtyEnable()) {
            $qtyIncrements = $this->productStatusConfig->getMinSaleConfigQty();
            if ($qtyIncrements && $this->mathDivision->getExactDivision($qty, $qtyIncrements) != 0) {
                throw new LocalizedException(
                    __(
                        'You can buy this product only in quantities of %1 at a time.',
                        $qtyIncrements
                    )
                );
            }
        }

        return [$itemId, $itemQty];
    }
}
