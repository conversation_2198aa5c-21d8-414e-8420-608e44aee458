<?php

namespace MadHat\DbLogger\Model\ResourceModel;


class DbLogger extends \Magento\Framework\Model\ResourceModel\Db\AbstractDb
{
    /**
     * @var string
     */
    protected $_idFieldName = 'entity_id';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init('madhat_db_logger', 'entity_id');
    }

    public function deleteDbLoggerBeforeDays(int $days)
    {
        $where = 'created_at < now() - interval ' . $days .' day';
        $this->getConnection()->delete($this->getMainTable(), $where);
    }
}
