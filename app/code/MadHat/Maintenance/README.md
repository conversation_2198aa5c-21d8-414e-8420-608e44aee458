# Mage2 Module MadHat Maintenance

    ``MadHat/Maintenance``

## Main Functionalities
With this module we can create CMS Block, CMS Page, and Categories using provided CSV and maintain version.

## Configuration
No Configuration needed.

## Usage
To create data please check the below CLI command. you can change version and csv file as per your requirement.

### For CMS Block
   ```php
   php bin/magento madhat:maintenance:data cms_block V1.2.0 --file-name='pages_static_blocks.csv'
   ```

### For CMS Pages
   ```php
   php bin/magento madhat:maintenance:data cms_page V1.2.0 --file-name='pages.csv'
   ```
### For Categories import via CSV
   ```php
   php bin/magento madhat:maintenance:data category V1.2.0 --file-name='categories.csv'
   ```


