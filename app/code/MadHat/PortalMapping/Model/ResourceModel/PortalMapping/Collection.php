<?php
/**
 * MadHat_PortalMapping extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_PortalMapping
 * @copyright Copyright (c) 2021
 **/

namespace MadHat\PortalMapping\Model\ResourceModel\PortalMapping;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'entity_id';
    /**
     * Define resource model.
     */
    protected function _construct()
    {
        $this->_init(
            'MadHat\PortalMapping\Model\PortalMapping',
            'MadHat\PortalMapping\Model\ResourceModel\PortalMapping'
        );
    }
}
