<?php

namespace Threepartment\Serialnumbers\Api\Data;

interface SerialInterface
{
    /**
     * Constants for keys of data array. Identical to the name of the getter in snake case.
     */
    const SERIAL_NUMBER_ID = 'serial_number_id';
    const PRODUCT_ID = 'product_id';
    const SKU = 'sku';
    const SERIAL_NUMBER = 'serial_number';
    const STATUS = 'status';
    const ORDER_ID = 'order_id';
    const DATE_CREATED = 'date_created';
    const DATE_UPDATED = 'date_updated';

   /**
    * Get SerialNumberId.
    *
    * @return int
    */
    public function getSerialNumberId();

   /**
    * Set SerialNumberId.
    */
    public function setSerialNumberId($serialNumberId);

   /**
    * Get ProductId.
    *
    * @return varchar
    */
    public function getProductId();

   /**
    * Set ProductId.
    */
    public function setProductId($productId);

   /**
    * Get SKU.
    *
    * @return varchar
    */
    public function getSku();

   /**
    * Set SKU.
    */
    public function setSku($sku);

   /**
    * Get SerialNumber.
    *
    * @return varchar
    */
    public function getSerialNumber();

   /**
    * Set SerialNumber.
    */
    public function setSerialNumber($serialNumber);

   /**
    * Get Status.
    *
    * @return varchar
    */
    public function getStatus();

   /**
    * Set Status.
    */
    public function setStatus($status);

   /**
    * Get OrderId.
    *
    * @return varchar
    */
    public function getOrderId();

   /**
    * Set OrderId.
    */
    public function setOrderId($orderId);

   /**
    * Get DateCreated.
    *
    * @return varchar
    */
    public function getDateCreated();

   /**
    * Set DateCreated.
    */
    public function setDateCreated($dateCreated);

    /**
    * Get DateUpdated.
    *
    * @return varchar
    */
    public function getDateUpdated();

   /**
    * Set DateUpdated.
    */
    public function setDateUpdated($dateUpdated);
}
