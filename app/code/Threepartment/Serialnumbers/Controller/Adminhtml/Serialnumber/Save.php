<?php


namespace Threepartment\Serialnumbers\Controller\Adminhtml\Serialnumber;

class Save extends \Magento\Backend\App\Action
{
    /**
     * @var \Threepartment\Serialnumbers\Model\SerialnumberFactory
     */
    var $gridFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Threepartment\Serialnumbers\Model\SerialnumberFactory $gridFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Threepartment\Serialnumbers\Model\SerialnumberFactory $gridFactory
    ) {
        parent::__construct($context);
        $this->gridFactory = $gridFactory;
    }

    /**
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (!$data) {
            $this->_redirect('threepartment_serialnumbers/serialnumber/addrow');
            return;
        }
        try {
            $rowData = $this->gridFactory->create();
            $rowData->setData($data);
            if (isset($data['id'])) {
                $rowData->setSerialNumberId($data['id']);
            }
            $rowData->save();
            if(!isset($data['is_ajax'])){
                $this->messageManager->addSuccess(__('Serial number has been successfully saved.'));
            }else{
                echo json_encode(array('response'=>'1', 'msg'=>__('Serial number has been successfully saved.')));
            }
        } catch (\Exception $e) {
            if($e->getMessage()=='Unique constraint violation found'){
                if(!isset($data['is_ajax'])){
                    $this->messageManager->addError(__('Duplicate Serial Number and SKU: ').'<b>('.$data['serial_number'].' / '.$data['sku'].')</b>');
                }else{
                    echo json_encode(array('response'=>'0', 'msg'=>__('Duplicate Serial Number and SKU: ').'<b>('.$data['serial_number'].' / '.$data['sku'].')</b>'));
                }
            }else{
                if(!isset($data['is_ajax'])){
                    $this->messageManager->addError(__($e->getMessage()));
                }else{
                    echo json_encode(array('response'=>'0', 'msg'=>__($e->getMessage())));
                }
            }
        }
        if(!isset($data['is_ajax'])){
            $this->_redirect('threepartment_serialnumbers/serialnumber/index');
        }
    }

    /**
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Threepartment_Serialnumbers::save');
    }
}
