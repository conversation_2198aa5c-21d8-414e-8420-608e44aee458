<?php
    $action_url = $this->getUrl('threepartment_serialnumbers/serialnumber/save');
?>
<script type='text/javascript'>
    require(['jquery', 'mage/mage', 'uiRegistry'], function($, mage, registry){
        $('#save').remove();
        $(document).ready( function() {
            var modal_class = '.threepartment_serialnumbers_serialnumber_listing_threepartment_serialnumbers_serialnumber_listing_test_modal';
            $('body').on('click','.product_form_product_form_serialnumber_fields button.action-basic', function(){
                prefillForm();
            });
            $('body').on('click', modal_class+' .page-actions-buttons .action-primary', function(e){
                var thisBtn = $(this);
                if(!$('#serial-dynamic-form').length){
                    $(modal_class+' fieldset').before("<form id='serial-dynamic-form' class='validate-data'><input type='submit' id='hidden-serial-submit-btn' value='' style='display:none'/><input type='hidden' value='1' name='is_ajax'/><div id='serial-submit-response-container' style='display:none'><div class='messages'><div class='message message-success success'><div id='serial-submit-response'></div></div></div></div></form>");
                    var fieldSet = $(modal_class+' fieldset').detach();
                    var spinnerHtml = '<div data-role="spinner" class="admin__data-grid-loading-mask" style="display: none;"><div class="spinner"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></div></div>';
                    $('#serial-dynamic-form').append(fieldSet);
                    $(modal_class+' input[name="product_id"]').attr('required', 'required');
                    $(modal_class+' input[name="sku"]').attr('required', 'required');
                    $(modal_class+' input[name="serial_number"]').attr('required', 'required');
                    $(modal_class+' .admin__fieldset-wrapper-content').prepend(spinnerHtml);
                }
                if(validForm()){
                    thisBtn.prop('disabled', true);
                    $(modal_class+' .admin__fieldset-wrapper-content .admin__data-grid-loading-mask').show();
                    $('#serial-submit-response-container').hide();
                    $.ajax({
                        type : 'POST',
                        url : '<?php echo $action_url;?>',
                        data : $('#serial-dynamic-form').serialize(),
                        success: function(msg){
                            $(modal_class+' .admin__fieldset-wrapper-content .admin__data-grid-loading-mask').hide();
                            thisBtn.prop('disabled',false);
                            msg = $.parseJSON(msg);
                            if(msg.response=='1'){
                                $('#serial-submit-response').html(msg.msg);
                                $('#serial-submit-response-container').show();
                                $('#serial-submit-response-container .message').removeClass('message-error').removeClass('error').addClass('success').addClass('message-success');
                                refreshSerialGrid();
                                $(modal_class+' input[name="product_id"]').val('');
                                $(modal_class+' input[name="sku"]').val('');
                                $(modal_class+' input[name="serial_number"]').val('');
                                $(modal_class+' input[name="order_id"]').val('');
                                prefillForm();
                            }else{
                                $('#serial-submit-response').html(msg.msg);
                                $('#serial-submit-response-container').show();
                                $('#serial-submit-response-container .message').addClass('message-error').addClass('error').removeClass('success').removeClass('message-success');
                            }
                        }
                    });
                }else{
                    $('#hidden-serial-submit-btn').click();
                }
            });
            function refreshSerialGrid(){
                var grid = 'threepartment_serialnumbers_serialnumber_listing.threepartment_serialnumbers_serialnumber_listing_data_source';
                if(grid) {
                    var params = [];
                    var target = registry.get(grid);
                    if (target && typeof target === 'object') {                                     
                        target.set('params.t ', Date.now());                
                    }
                }
            }
            function validForm(){
                return $(modal_class+' .admin__field-error').length==0 && 
                       $(modal_class+' input[name="product_id"]').val()!="" &&
                       $(modal_class+' input[name="sku"]').val()!="" && 
                       $(modal_class+' input[name="serial_number"]').val()!=""
            }
            function prefillForm(){
                var product_id = location.href.split('edit/id/')[1].split('/key')[0];
                if(!Number.isNaN(product_id)){
                    $(modal_class+' input[name="product_id"]').val(product_id);
                    if($('input[name="product[sku]"]').length)
                    $(modal_class+' input[name="sku"]').val($('input[name="product[sku]"]').val());
                }
            }
        });
    });
    
</script>
<?php
