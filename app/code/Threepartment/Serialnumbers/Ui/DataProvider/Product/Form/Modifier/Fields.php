<?php

namespace Threepartment\Serialnumbers\Ui\DataProvider\Product\Form\Modifier;

use Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\AbstractModifier;
use Magento\Ui\Component\Form\Fieldset;
use Magento\Ui\Component\Form\Field;
use Magento\Ui\Component\Modal;
use Magento\Ui\Component\Form\Element\Select;
use Magento\Ui\Component\Form\Element\Input;
use Magento\Ui\Component\Form\Element\DataType\Text;
use Magento\Catalog\Model\Locator\LocatorInterface;
use Magento\Framework\UrlInterface;


class Fields extends AbstractModifier{

    const GROUP_REVIEW = 'review';
    const GROUP_CONTENT = 'content';
    const DATA_SCOPE_REVIEW = 'grouped';
    const SORT_ORDER = 20;
    const LINK_TYPE = 'associated';

    /**
    * Fields constructor.
    *
    * @param \Magento\Catalog\Model\Locator\LocatorInterface $locator
    */
    public function __construct(LocatorInterface $locator, UrlInterface $urlBuilder){
        $this->locator = $locator;
        $this->urlBuilder = $urlBuilder;
    }

    public function modifyData(array $data){
        $productId = $this->locator->getProduct()->getId();
        $data[$productId][self::DATA_SOURCE_DEFAULT]['product_id'] = $productId;
        return $data;
    }
    public function modifyMeta(array $meta){
        if (!$this->locator->getProduct()->getId()) {
            return $meta;
        }
       $meta = array_replace_recursive(
           $meta,
           [
               'serialnumber' => [
                   'arguments' => [
                       'data' => [
                           'config' => [
                               'label' => __('Serial Numbers'),
                               'collapsible' => true,
                               'componentType' => Fieldset::NAME,
                               'dataScope' => 'data.serialnumber',
                               'sortOrder' => 10
                           ],
                       ],
                   ],
                   'children' => ['fields'=>$this->getFields()]
                   
               ],
           ]
       );
        
       return $meta;
    }
    protected function getFields(){
        
        return [
            'arguments' => [
                'data' => [
                    'config' => [
                        'autoRender' => true,
                        'componentType' => 'insertListing',
                        'dataScope' => 'threepartment_serialnumbers_serialnumber_listing',
                        'externalProvider' => 'threepartment_serialnumbers_serialnumber_listing.threepartment_serialnumbers_serialnumber_listing_data_source',
                        'selectionsProvider' => 'threepartment_serialnumbers_serialnumber_listing.threepartment_serialnumbers_serialnumber_listing_data_source.product_columns.ids',
                        'ns' => 'threepartment_serialnumbers_serialnumber_listing',
                        'render_url' => $this->urlBuilder->getUrl('mui/index/render'),
                        'realTimeLink' => false,
                        'behaviourType' => 'simple',
                        'externalFilterMode' => true,
                        'imports' => [
                            'productId' => '${ $.provider }:data.product.current_product_id'
                        ],
                        'exports' => [
                            'productId' => '${ $.externalProvider }:params.current_product_id'
                        ],
                    ]
                ],
            ],
        ];
    }
    protected function getModalAddFieldset(){

        return [
            'children' => [
                'button_set' => $this->getButtonSet(),
                'modal' => $this->getGenericModal(),
            ],
            'arguments' => [
                'data' => [
                    'config' => [
                        'additionalClasses' => 'admin__fieldset-section',
                        'label' => __('Manage Serial Numbers'),
                        'collapsible' => false,
                        'componentType' => Fieldset::NAME,
                        'dataScope' => '',
                        'sortOrder' => 10,
                    ],
                ],
            ]
        ];
    }
    protected function getURLButtonSet()
    {
        $content = __(
            'Serial numbers adds the ability to provide unique identification for each product unit.'
        );

        return [
            'arguments' => [
                'data' => [
                    'config' => [
                        'formElement' => 'container',
                        'componentType' => 'container',
                        'label' => false,
                        'content' => $content,
                        'template' => 'ui/form/components/complex',
                    ],
                ],
            ],
            'children' => [
                'button_modal' => [
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'formElement' => 'container',
                                'componentType' => 'container',
                                'component' => 'Magento_Ui/js/form/components/button',
                                'actions' => [
                                    [
                                        'targetName' => 'product_form.product_form.serialnumber.toggle.modal',
                                        'actionName' => 'toggleModal',
                                    ],
                                ],
                                'title' => __('Add New Serial Number'),
                                'provider' => null,
                            ],
                        ],
                    ],

                ],
            ],
        ];
    }
    protected function getButtonSet()
    {
        $content = __(
            'Serial numbers adds the ability to provide unique identification for each product unit.'
        );

        return [
            'arguments' => [
                'data' => [
                    'config' => [
                        'formElement' => 'container',
                        'componentType' => 'container',
                        'label' => false,
                        'content' => $content,
                        'template' => 'ui/form/components/complex',
                    ],
                ],
            ],
            'children' => [
                'button_serialmodal' => [
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'formElement' => 'container',
                                'componentType' => 'container',
                                'component' => 'Magento_Ui/js/form/components/button',
                                'actions' => [
                                    [
                                        'targetName' => 'product_form.product_form.serialnumber.toggle.modal',
                                        'actionName' => 'toggleModal',
                                    ],
                                ],
                                'title' => __('New Serial Number'),
                                'provider' => null,
                            ],
                        ],
                    ],

                ],
            ],
        ];
    }
    protected function getGenericModal(){
        $scope = "serialNumberModal";
        $listingTarget = $scope . '_product_listing';
        $title = "Add New Serial Number";
        
        $modal = [
            'arguments' => [
                'data' => [
                    'config' => [
                        'componentType' => Modal::NAME,
                        'dataScope' => '',
                        'options' => [
                            'title' => __($title),
                            'buttons' => [
                                [
                                    'text' => __('Cancel'),
                                    'actions' => [
                                        'closeModal'
                                    ]
                                ],
                                [
                                    'text' => __('Add Selected Products'),
                                    'class' => 'action-primary',
                                    'actions' => [
                                        [
                                            'targetName' => 'index = ' . $listingTarget,
                                            'actionName' => 'save'
                                        ],
                                        'closeModal'
                                    ]
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'children' => [
                'serialNumberModal' => [
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'autoRender' => false,
                                'componentType' => 'insertListing',
                                'dataScope' => $listingTarget,
                                'externalProvider' => $listingTarget . '.' . $listingTarget . '_data_source',
                                'selectionsProvider' => $listingTarget . '.' . $listingTarget . '.product_columns.ids',
                                'ns' => $scope,
                                'render_url' => $this->urlBuilder->getUrl('mui/index/render'),
                                'realTimeLink' => true,
                                'dataLinks' => [
                                    'imports' => false,
                                    'exports' => true
                                ],
                                'behaviourType' => 'simple',
                                'externalFilterMode' => true,
                                'imports' => [
                                    'productId' => '${ $.provider }:data.product.current_product_id',
                                    'storeId' => '${ $.provider }:data.product.current_store_id',
                                ],
                                'exports' => [
                                    'productId' => '${ $.externalProvider }:params.current_product_id',
                                    'storeId' => '${ $.externalProvider }:params.current_store_id',
                                ]
                            ],
                        ],
                    ],
                ],
            ],
        ];

        return $modal;
    }

}