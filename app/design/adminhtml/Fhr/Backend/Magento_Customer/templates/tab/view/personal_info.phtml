<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Customer\Block\Adminhtml\Edit\Tab\View\PersonalInfo $block */

$lastLoginDateAdmin = $block->getLastLoginDate();
$lastLoginDateStore = $block->getStoreLastLoginDate();

$createDateAdmin = $block->getCreateDate();
$createDateStore = $block->getStoreCreateDate();
$allowedAddressHtmlTags = ['b', 'br', 'em', 'i', 'li', 'ol', 'p', 'strong', 'sub', 'sup', 'ul'];
?>
<div class="fieldset-wrapper customer-information">
    <div class="fieldset-wrapper-title">
        <span class="title"><?= $block->escapeHtml(__('Personal Information')) ?></span>
    </div>
    <table class="admin__table-secondary">
        <tbody>
        <?= $block->getChildHtml() ?>
        <tr>
            <th><?= $block->escapeHtml(__('Last Logged In:')) ?></th>
            <td><?= $block->escapeHtml($lastLoginDateAdmin) ?> (<?= $block->escapeHtml($block->getCurrentStatus()) ?>)</td>
        </tr>
        <?php if ($lastLoginDateAdmin != $lastLoginDateStore) : ?>
            <tr>
                <th><?= $block->escapeHtml(__('Last Logged In (%1):', $block->getStoreLastLoginDateTimezone())) ?></th>
                <td><?= $block->escapeHtml($lastLoginDateStore) ?> (<?= $block->escapeHtml($block->getCurrentStatus()) ?>)</td>
            </tr>
        <?php endif; ?>
        <tr>
            <th><?= $block->escapeHtml(__('Account Lock:')) ?></th>
            <td><?= $block->escapeHtml($block->getAccountLock()) ?></td>
        </tr>
        <tr>
            <th><?= $block->escapeHtml(__('Confirmed email:')) ?></th>
            <td><?= $block->escapeHtml($block->getIsConfirmedStatus()) ?></td>
        </tr>
        <tr>
            <th><?= $block->escapeHtml(__('Account Created:')) ?></th>
            <td><?= $block->escapeHtml($createDateAdmin) ?></td>
        </tr>
        <?php if ($createDateAdmin != $createDateStore) : ?>
            <tr>
                <th><?= $block->escapeHtml(__('Account Created on (%1):', $block->getStoreCreateDateTimezone())) ?></th>
                <td><?= $block->escapeHtml($createDateStore) ?></td>
            </tr>
        <?php endif; ?>
        <tr>
            <th><?= $block->escapeHtml(__('Account Created in:')) ?></th>
            <td><?= $block->escapeHtml($block->getCreatedInStore()) ?></td>
        </tr>
        <tr>
            <th><?= $block->escapeHtml(__('Customer Group:')) ?></th>
            <td><?= $block->escapeHtml($block->getGroupName()) ?></td>
        </tr>
        <tr>
            <th><?= $block->escapeHtml(__('Customer ID:')) ?></th>
            <td><?= $block->escapeHtml($block->getCustomer()->getId()) ?></td>
        </tr>
        </tbody>
    </table>
    <address>
        <strong><?= $block->escapeHtml(__('Default Billing Address')) ?></strong><br/>
        <?= $block->escapeHtml($block->getBillingAddressHtml(), $allowedAddressHtmlTags) ?>
    </address>

</div>
