//** Popover body background color
@popover-bg:                        #fff;

@popover-inverse-bg:				#333;

@popover-inverse-color:				#eee;
//** Popover min width
@popover-min-width:                   50px;

@popover-min-height:                  32px;

//** Popover border color
@popover-border-color:                rgba(0,0,0,.2);

//** Popover fallback border color
@popover-fallback-border-color:       #ccc;

//** Popover title background color
@popover-title-bg:                    @popover-bg;
@popover-inverse-title-bg:            @popover-inverse-bg;

//** Popover arrow width
@popover-arrow-width:                 10px;
//** Popover arrow color
@popover-arrow-color:                 #fff;

//** Popover outer arrow width
@popover-arrow-outer-width:           (@popover-arrow-width + 1);
//** Popover outer arrow color
@popover-arrow-outer-color:           fadein(@popover-border-color, 5%);
//** Popover outer arrow fallback color
@popover-arrow-outer-fallback-color:  darken(@popover-fallback-border-color, 20%);

@popover-border-radius-base:        4px;
@popover-border-radius-large:       6px;
@popover-border-radius-small:       3px;

@popover-font-size-base:          14px;
@popover-font-size-large:         ceil((@popover-font-size-base * 1.25)); // ~18px
@popover-font-size-small:         ceil((@popover-font-size-base * 0.85)); // ~12px


@popover-close-size:                16px;
@popover-close-color:               #000;

@popover-z-index: 9999;
@popover-backdrop-z-index: (@popover-z-index - 1);

.box-shadow(@shadow) {
  -webkit-box-shadow: @shadow; // iOS <4.3 & Android <4.1
          box-shadow: @shadow;
}

.opacity(@opacity) {
  opacity: @opacity;
  // IE8 filter
  @opacity-ie: (@opacity * 100);
  filter: ~"alpha(opacity=@{opacity-ie})";
}

.animation(@animation) {
  -webkit-animation: @animation;
       -o-animation: @animation;
          animation: @animation;
}

.transform(@transform){
    -webkit-transform: @transform;
         -o-transform: @transform;
            transform: @transform;
}

.transition(@transition){
   -webkit-transition: @transition;
         -o-transition: @transition;
            transition: @transition;
}

.transition-property(@property){
    -webkit-transition-property: @property;
         -o-transition-property: @property;
            transition-property: @property;
}

.webui-popover-content {
  display: none;
}

.webui-popover-rtl{
   direction: rtl;
   text-align: right;
}

/*  webui popover  */
.webui-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: @popover-z-index;
  display: none;
  min-width: @popover-min-width;
  min-height:@popover-min-height;
  padding: 1px;
  text-align: left;
  white-space: normal;
  background-color: @popover-bg;
  background-clip: padding-box;
  border: 1px solid @popover-fallback-border-color;
  border: 1px solid @popover-border-color;
  border-radius: @popover-border-radius-large;
  .box-shadow(0 5px 10px rgba(0,0,0,.2));

  &.top,&.top-left,&.top-right     { margin-top: -@popover-arrow-width; }
  &.right,&.right-top,&.right-bottom   { margin-left: @popover-arrow-width; }
  &.bottom,&.bottom-left,&.bottom-right { margin-top: @popover-arrow-width; }
  &.left,&.left-top,&.left-bottom    { margin-left: -@popover-arrow-width; }


  &.pop {
    .transform(scale(.8));
    .transition(transform .15s cubic-bezier(.3, 0, 0, 1.5));
    .opacity(0);
  }
  &.pop-out {
    .transition-property("opacity,transform");
    .transition(0.15s linear);
    .opacity(0);
  }

  &.fade,&.fade-out{
    .transition(opacity .15s linear);
    .opacity(0);
  }
  &.out{
    .opacity(0);
  }

  &.in {.transform(none);.opacity(1)};

  .webui-popover-content {
    padding: 9px 14px;
    overflow: auto;
    display: block;
    > div:first-child {
      width: 99%;
    }
  }
}



.webui-popover-inner .close{
  font-family: arial;
  margin:8px 10px 0 0;
  float: right;
  font-size: @popover-close-size;
  font-weight: bold;
  line-height: @popover-close-size;
  color: @popover-close-color;
  text-shadow: 0 1px 0 #fff;
  .opacity(.2);
  text-decoration: none;
  &:hover,&:focus{
  	.opacity(.5);
  }
  &:after{
    content: "\00D7";
    width:0.8em;
    height:0.8em;
    padding: 4px;
    //font-family: 'Times New Roman';
    position: relative;
  }
}

.webui-popover-title {
  padding: 8px 14px;
  margin: 0;
  font-size: @popover-font-size-base;
  font-weight: bold;
  line-height: 18px;
  background-color: @popover-title-bg;
  border-bottom: 1px solid darken(@popover-title-bg, 5%);
  border-radius: (@popover-border-radius-large - 1) (@popover-border-radius-large - 1) 0 0;
}

.webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
  display: none;

}

.webui-popover-inverse{
  background-color:@popover-inverse-bg;
  color:@popover-inverse-color;

  .webui-popover-title{
	  background: @popover-inverse-title-bg;
	  border-bottom: 1px solid  lighten(@popover-inverse-title-bg, 3%);
	  color:@popover-inverse-color;
	}
}

.webui-no-padding {
	.webui-popover-content {
		padding: 0;
	}
	.list-group-item{
		border-right: none;
  		border-left:none;
  		&:first-child{
			border-top:0;
  		}
  		&:last-child{
			border-bottom:0;
  		}
	}
}


.webui-popover > .webui-arrow{
  &,&:after{
		position: absolute;
		display: block;
		width: 0;
		height: 0;
		border-color: transparent;
		border-style: solid;
  }
}

.webui-popover > .webui-arrow {
  border-width: @popover-arrow-outer-width;
}
.webui-popover > .webui-arrow:after {
  border-width: @popover-arrow-width;
  content: "";
}

.webui-popover{
  &.top >.webui-arrow,
  &.top-right >.webui-arrow,
  &.top-left >.webui-arrow
  {
 	  bottom: -@popover-arrow-outer-width;
	  left: 50%;
	  margin-left: -@popover-arrow-outer-width;
	  border-top-color: @popover-arrow-outer-fallback-color; // IE8 fallback
	  border-top-color: @popover-arrow-outer-color;
	  border-bottom-width: 0;
	  &:after{
	  	  content: " ";
	  	  bottom: 1px;
		  margin-left: -@popover-arrow-width;
		  border-top-color: @popover-arrow-color;
		  border-bottom-width: 0;
	  }
  }
  &.right > .webui-arrow,
  &.right-top > .webui-arrow,
  &.right-bottom > .webui-arrow {
    top: 50%;
    left: -@popover-arrow-outer-width;
    margin-top: -@popover-arrow-outer-width;
    border-left-width: 0;
    border-right-color: @popover-arrow-outer-fallback-color; // IE8 fallback
    border-right-color: @popover-arrow-outer-color;
    &:after {
      content: " ";
      left: 1px;
      bottom: -@popover-arrow-width;
      border-left-width: 0;
      border-right-color: @popover-arrow-color;
    }
  }
  &.bottom >.webui-arrow,
  &.bottom-right >.webui-arrow,
  &.bottom-left >.webui-arrow
  {
 	  top: -@popover-arrow-outer-width;
	  left: 50%;
	  margin-left: -@popover-arrow-outer-width;
	  border-bottom-color: @popover-arrow-outer-fallback-color; // IE8 fallback
	  border-bottom-color: @popover-arrow-outer-color;
	  border-top-width: 0;
	  &:after{
	  	  content: " ";
	  	  top: 1px;
		  margin-left: -@popover-arrow-width;
		  border-bottom-color: @popover-arrow-color;
		  border-top-width: 0;
	  }
  }
  &.left > .webui-arrow,
  &.left-top > .webui-arrow,
  &.left-bottom > .webui-arrow {
    top: 50%;
    right: -@popover-arrow-outer-width;
    margin-top: -@popover-arrow-outer-width;
    border-right-width: 0;
    border-left-color: @popover-arrow-outer-fallback-color; // IE8 fallback
    border-left-color: @popover-arrow-outer-color;
    &:after {
      content: " ";
      right: 1px;
      border-right-width: 0;
      border-left-color: @popover-arrow-color;
      bottom: -@popover-arrow-width;
    }
  }
}

.webui-popover-inverse{
	&.top > .webui-arrow,
	&.top-left > .webui-arrow,
	&.top-right > .webui-arrow{
		&,&:after{
			border-top-color: @popover-inverse-bg;
		}
	}
	&.right > .webui-arrow,
  &.right-top > .webui-arrow,
  &.right-bottom > .webui-arrow{
		&,&:after{
			border-right-color: @popover-inverse-bg;
		}
	}
	&.bottom > .webui-arrow,
	&.bottom-left > .webui-arrow,
	&.bottom-right > .webui-arrow{
		&,&:after{
			border-bottom-color: @popover-inverse-bg;
		}
	}
	&.left > .webui-arrow,
  &.left-top > .webui-arrow,
  &.left-bottom > .webui-arrow{
		&,&:after{
			border-left-color: @popover-inverse-bg;
		}
	}
}

.webui-popover i.icon-refresh:before{
  content: "";
}

.webui-popover i.icon-refresh{
   display: block;
   width:30px;
   height:30px;
   font-size: 20px;
   top:50%;
   left:50%;
   position: absolute;
   margin-left:-15px;
   margin-right:-15px;
   background:url(../img/loading.gif) no-repeat;
   // .animation(rotate 1s linear 0 infinite);
}

@-webkit-keyframes rotate {
  100% {-webkit-transform: rotate(360deg);}
}

@keyframes rotate {
  100% {transform: rotate(360deg);}
}

.webui-popover-backdrop {
  background-color: rgba(0, 0, 0, 0.65);
  width:100%;
  height:100%;
  position: fixed;
  top:0;
  left:0;
  z-index: @popover-backdrop-z-index;
}

.webui-popover {
    //Compatible with bootstrap dropdown-menu
    .dropdown-menu{
      display: block;
      position:relative;
      top:0;
      border:none;
      box-shadow:none;
      float:none;
  }
}



