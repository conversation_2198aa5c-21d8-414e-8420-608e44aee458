<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <referenceBlock name="category.products.list">
                <action method="setDefaultDirection">
                    <argument name="dir" xsi:type="string">asc</argument>
                </action>
            </referenceBlock>
        </referenceContainer>
        <move element="category.view.container" destination="content" before="-"/>
    </body>
</page>