& when (@media-common = true) {
  #fhr-available-only {
    &.available-only{
      .action.primary{
        background-color: #4db72a;
        border-color: #38a047;
      }
    }
    &.all-products {
      .action.primary {
        background: #F6F7F9;
        color: #0088CC;
      }
    }
  }

  .product-image-photo {
    height: 100%;
    width: auto;
  }

  .checkout-cart-index {
    .product-image-photo {
      height: auto;
    }
  }

  .products-grid {
    .product-item-info{
      &:hover{
        .product-item-details {
          .product-item-actions {
            .tocart {
              &.salable {
                background-color: #4db72a;
                border-color: #38a047;
                color: #fff;
                opacity: 0.9;
              }
              &.not-salable {
                background-color: #d3222a;
                border-color: #ba0001;
                color: #fff;
                opacity: 0.9;
              }
            }
          }
        }
      }
    }
    .product-item-details {
      .product-item-actions {
        .tocart {
          &.salable {
            background-color: #4db72a;
            border-color: #38a047;
            color: #fff;
          }

          &.not-salable {
            background-color: #d3222a;
            border-color: #ba0001;
            color: #fff;
          }
        }
      }
      .product-item-name {
        height: 4em;
        overflow: hidden;
      }
    }
  }

  .action.primary {
    &:hover{
      &.salable {
        background-color: #4db72a;
        border-color: #38a047;
        color: white;
        opacity: 0.9;
      }

      &.not-salable {
        background-color: #d3222a;
        border-color: #ba0001;
        color: white;
        opacity: 0.9;
      }
    }
    &.salable {
      background-color: #4db72a;
      border-color: #38a047;
      color: white;
    }

    &.not-salable {
      background-color: #d3222a;
      border-color: #ba0001;
      color: white;
    }
  }
}