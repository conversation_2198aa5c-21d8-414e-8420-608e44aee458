<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Sales\Block\Order\Email\Invoice\Items $block */
/** @var \Magento\Framework\Escaper $escaper */

?>
<?php $_invoice = $block->getInvoice() ?>
<?php $_order   = $block->getOrder() ?>
<?php if ($_invoice && $_order): ?>
    <table class="email-items">
        <thead>
            <tr>
                <th class="item-info">
                    <?= $escaper->escapeHtml(__('Items')) ?>
                </th>
                <th class="item-qty">
                    <?= $escaper->escapeHtml(__('Qty')) ?>
                </th>
                <th class="item-subtotal">
                    <?= $escaper->escapeHtml(__('Subtotal')) ?>
                </th>
            </tr>
        </thead>
        <?php foreach ($_invoice->getAllItems() as $_item): ?>
            <?php if (!$_item->getOrderItem()->getParentItem()): ?>
                <tbody>
                    <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
        <tfoot class="order-totals">
            <?= $block->getChildHtml('invoice_totals') ?>
        </tfoot>
    </table>
<?php endif; ?>
