<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Update to your %store_name invoice" store_name=$store.frontend_name}} @-->
<!--@vars {
"var order_data.customer_name":"Guest Customer Name",
"var comment|escape|nl2br":"Invoice Comment",
"var invoice.increment_id":"Invoice Id",
"var order.increment_id":"Order Id",
"var order_data.frontend_status_label":"Order Status",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours"
} @-->
{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <h1 class="page-title more-padding">{{trans "Your order is %order_status %customer_name" customer_name=$order_data.customer_name order_status=$order_data.frontend_status_label}}</h1>
            
            <p>
                {{trans
                    "Your order <strong>#%increment_id</strong> has been updated with a status of <strong>%order_status</strong>."

                    increment_id=$order.increment_id
                    order_status=$order_data.frontend_status_label
                |raw}}
                {{trans 'You can check the status of your order by <a href="%account_url">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
            </p>
            <p class="mt-20">
                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}.
            </p>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            {{depend comment}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var comment|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
