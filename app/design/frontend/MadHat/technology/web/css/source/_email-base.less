// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//  The contents of this file will get split into two output files: email.less and email-inline.less.
//
//  email.less
//  Styles contained in the .email-non-inline() and .media-width() mixins will be output in the email.less file
//  and will be included in a <style> tag in emails. Non-inline styles should be used for styles that can't be applied
//  as inline CSS, such as media queries.
//
//  email-inline.less
//  Styles not included in the mixins mentioned above will be included in the email-inline.less file.
//  Since email client support for CSS in <style> tags is limited, the majority of styles should be added as "inline"
//  styles. CSS will be applied to transactional email HTML via the Emogrifier library, which only has support for
//  certain CSS selectors (source: https://github.com/jjriv/emogrifier#supported-css-selectors):
//
//  Supported selectors (examples in parenthesis):
//      * ID (#logo)
//      * class (.logo)
//      * type (h1)
//      * descendant (.logo > a)
//      * child (.logo a)
//      * adjacent (.logo + .second-example)
//      * attribute presence (a[title])
//      * attribute value (a[title="example"])
//      * attribute only ([title="example"])
//
//  Unsupported selectors (examples in parenthesis):
//      * first-child (div:first-child)
//      * last-child (div:last-child)
//      * nth-child (div:nth-child(3n+1))
//      * universal (*)
//      * pseudo (a:hover, a:active, a:focus, span:before, span:after, etc)

//
//  Variables
//  _____________________________________________

@font-size-unit-convert: false; // Prevents font-related measurements from being converted to the incompatible rem unit

//
//  Resets
//  ---------------------------------------------

//  Generic resets

body {
    margin: 0;
    padding: 0;
}

img {
    border: 0;
    height: auto;
    line-height: 100%;
    outline: none;
    text-decoration: none;
}

table {
    border-collapse: collapse;

    td {
        vertical-align: top;
    }
}

//  Client-specific resets (from the Salted email template: https://github.com/rodriguezcommaj/salted)

.email-non-inline() {
    //  Prevent WebKit and Windows mobile from changing default text sizes
    body,
    table,
    td,
    a {
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
    }

    //  Allow smoother rendering of resized images in Internet Explorer (such as high-resolution logo)
    img {
        -ms-interpolation-mode: bicubic;
    }

    //  Remove spacing between tables in Outlook 2007 and up
    table,
    td {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
    }
}

//
//  Typography
//  ---------------------------------------------

//  See email-fonts.less for @font-face declarations

.lib-typography-all();

body {
    font-family: @font-family__base;
    font-weight: normal;
    text-align: left;
}

th,
td {
    font-family: @font-family__base;
}

a {
    color: @link__color;
    text-decoration: @link__text-decoration;
}

.email-non-inline() {
    a:visited {
        color: @link__visited__color !important;
        text-decoration: @link__visited__text-decoration !important;
    }

    a:hover {
        color: @link__hover__color !important;
        text-decoration: @link__hover__text-decoration !important;
    }

    a:active {
        color: @link__active__color !important;
        text-decoration: @link__active__text-decoration !important;
    }

    //  Remove link color on iOS
    .no-link a {
        color: @text__color !important;
        cursor: default !important;
        text-decoration: none !important;
    }
}

//
//  Layout
//  ---------------------------------------------

html,
body {
    background-color: @email__background-color;
}

.wrapper {
    margin: 0 auto;
}

.wrapper-inner {
    padding-bottom: @email-content__padding__l;
    width: 100%;
}

.main {
    margin: 0 auto;
    text-align: left; // Necessary to prevent all text from centering in Outlook 2003
    width: @email-body__width;
}

.header {
    padding: @email-body__padding @email-body__padding 0;
}

.main-content {
    background-color: @email-content__background-color;
    padding: @email-body__padding;
}

.footer {
    padding: 0 @email-body__padding @email-body__padding; // Reduce space between body of email and closing text
}

//  Layout (Mobile)
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    html,
    body {
        //  Change background/foreground to same color
        background-color: @email-content__background-color;
        width: 100% !important;
    }

    .main {
        max-width: 100% !important;
        min-width: 240px;
        width: auto !important;
    }
}

//
//  Buttons
//  ---------------------------------------------

.button {
    & > tr > td {
        padding-bottom: @indent__s;
    }

    .inner-wrapper {
        td {
            .lib-css(border-radius, @button__border-radius, 1);
            background-color: @button-primary__background;

            a {
                .lib-css(border-radius, @button__border-radius, 1);
                border: 1px solid @button-primary__background;
                color: @button-primary__color;
                display: inline-block;
                font-size: @button__font-size;
                padding: @button__padding;
                text-decoration: none;
            }
        }
    }
}

.email-non-inline() {
    .button {
        .inner-wrapper {
            td:hover {
                background-color: @button-primary__hover__background !important;
            }

            a:active,
            td:active {
                background-color: @button-primary__active__background !important;
            }
        }

        a:active,
        a:hover,
        a:visited {
            //  Undo general link hover state
            border: 1px solid @button-primary__hover__background;
            color: @button-primary__hover__color !important;
            text-decoration: none !important;
        }
    }

    .media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
        .button {
            .inner-wrapper {
                width: 100% !important;

                td {
                    a {
                        font-size: @button__font-size + 2;
                    }
                }
            }
        }
    }
}

//
//  Messages
//  ---------------------------------------------

.message-info,
.message-gift {
    width: @table__width;

    td {
        background-color: @message-email__background;
        border: 1px solid @message-email__border-color;
        color: @message-email__color;
        margin: 0;
        padding: @email-content__padding__base;

        a {
            color: @message-email-link__color;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
        }
    }
}

.message-details {
    margin-bottom: @indent__s;

    b {
        font-weight: bold;
    }

    td {
        padding-bottom: @indent__xs;

        b {
            margin-right: @indent__s;
        }
    }
}
