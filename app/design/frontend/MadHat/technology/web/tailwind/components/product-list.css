.sidebar-main {
    .bg-container-darker {
        @apply bg-cgrey-30 md:bg-white rounded-sm border-0 p-2 md:p-0;

        .block-title {
            @apply text-base md:hidden;

            .bg-container-lighter.rounded.border {
                @apply p-0 border-0 bg-transparent;
            }
        }
    }
}
.filter-content {

    .filter-option {
        border: 1px solid #707070;
        @apply mb-2 py-2;

        h3 {
            @apply text-cgrey-65;
        }
    }
    
    .filter-current {
        @apply p-2 border-cgrey-65;

        .filter-options-title {
            @apply border-b border-cgrey-65 mb-2 pb-2;
        }
        #active-filtering-content {
            > div {
                @apply mb-1 pb-1;
            }
        }
    }

    .filter-options-title {
        strong,
        .title {
            @apply text-base md:text-lg text-cgrey-65;
        }

        .py-1.px-1.rounded.border.border-container {
            @apply border-none;
        }
    }
}

.product-item {
    .product-item-top {
        @apply mb-1;
    }
    
    &:hover {
        .product-item-top {
            @apply shadow-lg;
        }
    }
    .price-container {
        @apply block;

        .price {
            @apply font-semibold text-lg;
        }

        .price-label {
            @apply text-sm;
        }
    }

    .special-price .price-container .price-label {
        @apply sr-only;
    }

    .old-price .price-container {
        @apply text-gray-500;

        .price {
            @apply font-normal text-xs;
        }
    }
}

.product-info {
    .product-sub-title {
        .product-item-link {
            @apply capitalize;
        }
    }
}

.color-size-weight {
    @apply text-xs mb-2 ml-4 min-h-20;
    ul {
        @apply list-disc;
    }
}
