<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Framework\Escaper;

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Product $product */
$product = $block->getProduct();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
$tierPrices = $productPriceViewModel->getTierPrices(TierPrice::PRICE_CODE, $product);

if ($productPriceViewModel->displayPriceInclAndExclTax()) {
    $regularPriceExclTax = $productPriceViewModel->getPriceValueExclTax(RegularPrice::PRICE_CODE, $product);
    $finalPriceExclTax = $productPriceViewModel->getPriceValueExclTax(FinalPrice::PRICE_CODE, $product);
}

$displayTax = $productPriceViewModel->displayPriceIncludingTax();

?>
<script>
    function initPrice<?= (int)$product->getId() ?>() {

        <?php /* All four of these keys are used - they are rendered by PHP */ ?>
        const regularPriceInclTaxKey = 'oldPrice',
              regularPriceExclTaxKey = 'baseOldPrice',
              finalPriceInclTaxKey = 'finalPrice',
              finalPriceExclTaxKey = 'basePrice';

        function calculateCustomOptionPrices(activeCustomOptions, customOptionPrices) {
            return activeCustomOptions.reduce((priceAccumulator, activeCustomOptionId) => {
                const customOptionPrice = customOptionPrices[activeCustomOptionId];
                if (customOptionPrice) {
                    return Number.parseFloat(priceAccumulator) + Number.parseFloat(customOptionPrice);
                }
                return priceAccumulator;
            }, 0);
        }

        return {
            regularPriceKey: <?= $displayTax ? 'regularPriceInclTaxKey' : 'regularPriceExclTaxKey' ?>,
            finalPriceKey: <?= $displayTax ? 'finalPriceInclTaxKey' : 'finalPriceExclTaxKey' ?>,
            activeProductsPriceData: false,
            initialFinalPrice: <?= (float)$finalPrice ?>,
            calculatedFinalPrice: false,
            calculatedFinalPriceWithCustomOptions: false,
            initialTierPrices: <?= /** @noEscape */ json_encode($tierPrices) ?>,
            showRegularPriceLabel: <?= ($finalPrice < $regularPrice) ? 'true' : 'false' ?>,
            customOptionPrices: [],
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            initialBasePrice: <?= (float)$finalPriceExclTax ?>,
            calculatedBasePrice: false,
            customOptionBasePrices: [],
            calculatedBasePriceWithCustomOptions: false,
            <?php endif; ?>
            activeCustomOptions: [],
            qty: 1,
            updateCustomOptionActive(data) {
                let activeCustomOptions = this.activeCustomOptions;
                const customOptionId = data.customOptionId;

                if (data.active) {
                    if (!activeCustomOptions.includes(customOptionId)) {
                        activeCustomOptions.push(data.customOptionId);
                    }
                } else {
                    if (customOptionId && activeCustomOptions.includes(customOptionId)) {
                        let index = activeCustomOptions.indexOf(customOptionId);
                        activeCustomOptions.splice(index, 1);
                    }
                }
                this.calculateFinalPriceWithCustomOptions()
            },
            updateCustomOptionPrices(prices, basePrices) {
                if (prices) {
                    this.customOptionPrices = prices;
                }

                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                if (basePrices) {
                    this.customOptionBasePrices = basePrices;
                }
                <?php endif; ?>

                this.calculateFinalPriceWithCustomOptions();
            },
            calculateFinalPrice() {

                const findApplicableTierPrice = (initialPrice, withTax) => {
                    if (this.activeProductsPriceData && this.activeProductsPriceData.tierPrices) {
                        const key = withTax ? 'price' : 'basePrice'
                        return this.activeProductsPriceData.tierPrices.reduce((acc, tierPrice) => {
                            if (this.qty >= tierPrice.qty && tierPrice[key] < acc) {
                                return tierPrice[key];
                            }
                            return acc;
                        }, this.activeProductsPriceData[withTax ? finalPriceInclTaxKey : finalPriceExclTaxKey].amount);

                    } else {
                        const key = withTax ? 'price_incl_tax' : 'price_excl_tax';
                        return Object.values(this.initialTierPrices).reduce((acc, tierPrice) => {
                            if (this.qty >= tierPrice.price_qty && tierPrice[key] < acc) {
                                return tierPrice[key];
                            }
                            return acc;
                        }, initialPrice);

                    }
                }

                this.calculatedFinalPrice = findApplicableTierPrice(this.initialFinalPrice, <?= $displayTax ? 'true' : 'false' ?>);
                window.dispatchEvent(new CustomEvent("update-product-final-price", {detail: this.calculatedFinalPrice}));

                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                this.calculatedBasePrice = findApplicableTierPrice(<?= (float) $finalPriceExclTax ?>, false);
                window.dispatchEvent(new CustomEvent("update-product-base-price", {detail: {basePrice: this.calculatedBasePrice}}));
                <?php endif; ?>
            },
            calculatePriceLabelVisibility() {
                this.showRegularPriceLabel =
                    (this.calculatedFinalPrice === this.activeProductsPriceData[this.regularPriceKey].amount) &&
                    this.activeProductsPriceData.isMinimalPrice;
            },
            calculateFinalPriceWithCustomOptions() {
                const finalPrice = this.calculatedFinalPrice || this.initialFinalPrice;
                this.calculatedFinalPriceWithCustomOptions = finalPrice + this.getCustomOptionPrice();
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                const basePrice = this.calculatedBasePrice || this.initialBasePrice;
                this.calculatedBasePriceWithCustomOptions = basePrice + this.getCustomOptionBasePrice();
                <?php endif; ?>
            },
            getCustomOptionPrice() {
                return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionPrices);
            },
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            getCustomOptionBasePrice() {
                return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionBasePrices);
            },
            <?php endif; ?>
            getFormattedFinalPrice() {
                return hyva.formatPrice(
                    this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice
                )
            },
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            getFormattedBasePrice() {
                return hyva.formatPrice(
                    this.calculatedBasePriceWithCustomOptions ||
                    this.calculatedBasePrice ||
                    this.initialBasePrice
                )
            },
            <?php endif; ?>
            isPriceHidden() {
                const finalPrice = this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice;
                return <?= $product->isSaleable() ? 'false' : 'true' ?> && finalPrice === 0;
            },
            eventListeners: {
                ['@update-prices-<?= (int)$product->getId() ?>.window'](event) {
                    this.activeProductsPriceData = event.detail;

                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                    this.calculatePriceLabelVisibility();
                },
                ['@update-qty-<?= (int)$product->getId() ?>.window'](event) {
                    this.qty = event.detail;
                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                },
                ['@update-custom-option-active.window'](event) {
                    this.updateCustomOptionActive(event.detail);
                },
                ['@update-custom-option-prices.window'](event) {
                    this.updateCustomOptionPrices(event.detail);
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                ['@update-custom-option-base-prices.window'](event) {
                    this.updateCustomOptionPrices(null, event.detail);
                }
                <?php endif; ?>
            }
        }
    }
</script>
<div x-data="initPrice<?= (int)$product->getId() ?>()"
     x-bind="eventListeners"
     class="price-box price-final_price"
>
    <template x-if="!activeProductsPriceData && !isPriceHidden()">
        <div class="price-container">
            <?php if ($finalPrice < $regularPrice): ?>
                <div class="old-price mr-2 flex">
                    <span id="product-price-<?= (int)$product->getId() ?>"
                          class="price-wrapper title-font font-regular text-base line-through text-gray-900">
                        <span class="price" x-html="hyva.formatPrice(<?= (float)$regularPrice ?> + getCustomOptionPrice())">
                            <?= /** @noEscape */ $productViewModel->format($regularPrice) ?>
                        </span>
                    </span>
                </div>
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                <div class="old-price-excl-tax">
                    <span class="font-regular line-through text-gray-900">
                        <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                        <span class="price" x-html="hyva.formatPrice(<?= (float)$regularPriceExclTax ?> + getCustomOptionBasePrice())">
                            <?= /** @noEscape */ $productViewModel->format($regularPriceExclTax) ?>
                        </span>
                    </span>
                </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="final-price inline-block" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer">
                    <span class="price-label block">
                        <?= ($product->canConfigure() && is_int($product->getPrice())) ?
                            $escaper->escapeHtml(__('As low as')) . ':' :
                            '' ?>
                    </span>
                <span id="product-price-<?= (int)$product->getId() ?>"
                      class="price-wrapper pdp-price">
                    <span class="price" x-html="getFormattedFinalPrice()">
                        <?= /** @noEscape */ $productViewModel->format($finalPrice) ?>
                    </span>
                </span>
                <meta itemprop="price" content="<?= $escaper->escapeHtmlAttr($finalPrice) ?>">
                <meta itemprop="priceCurrency"
                      content="<?= $escaper->escapeHtmlAttr($productViewModel->getCurrencyData()['code']) ?>">
            </div>
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                <div class="final-price-excl-tax">
                    <span class="font-regular text-gray-900">
                        <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                        <span class="price" x-html="getFormattedBasePrice()">
                            <?= /** @noEscape */ $productViewModel->format($finalPriceExclTax) ?>
                        </span>
                    </span>
                </div>
            <?php endif; ?>
        </div>
    </template>
    <template x-if="activeProductsPriceData &&
        activeProductsPriceData.oldPrice &&
        activeProductsPriceData[finalPriceKey].amount < activeProductsPriceData[regularPriceKey].amount
    ">
        <div class="old-price flex mr-2">
            <span id="product-price-<?= (int)$product->getId() ?>"
                  class="price-wrapper title-font font-regular text-base line-through text-gray-900">
                <span class="price" x-html="hyva.formatPrice(activeProductsPriceData[regularPriceKey].amount + getCustomOptionPrice())"></span>
            </span>
        </div>
    </template>
    <template x-if="activeProductsPriceData">
        <div class="final-price inline-block">
            <?php if ($product->canConfigure() && is_int($product->getPrice())): ?>
                <span class="price-label block" :class="{ 'invisible' : !showRegularPriceLabel }">
                    <?= $escaper->escapeHtml(__('As low as')) ?>:
                </span>
            <?php endif; ?>
            <span id="product-price-<?= (int)$product->getId() ?>"
                  class="price-wrapper pdp-price">
                <span class="price" x-html="getFormattedFinalPrice()"></span>
            </span>
        </div>
    </template>
    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
    <template x-if="activeProductsPriceData &&
        activeProductsPriceData.oldPrice &&
        activeProductsPriceData[finalPriceKey].amount < activeProductsPriceData[regularPriceKey].amount
    ">
        <div class="old-price-excl-tax">
            <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
            <span class="font-regular text-gray-900">
                <span class="price" x-html="hyva.formatPrice(activeProductsPriceData['baseOldPrice'].amount + getCustomOptionBasePrice())"></span>
            </span>
        </div>
    </template>
    <template x-if="activeProductsPriceData">
        <div class="price-excl-taxinline-block">
            <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
            <span class="font-regular text-gray-900">
                <span class="price" x-html="getFormattedBasePrice()"></span>
            </span>
        </div>
    </template>
    <?php endif; ?>
</div>
