<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

// phpcs:disable Generic.Files.LineLength
?>

<?php if ($block->isEnabledViewSwitcher()): ?>
    <?php $modes = $block->getModes(); ?>
    <?php if ($modes && count($modes) > 1): ?>
        <nav
            class="modes flex mr-2 gap-4 items-center w-28 order-1 col-span-1 sm:col-span-2 md:col-span-1 lg:col-span-2"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Products view mode')) ?>"
        >
            <?php foreach ($block->getModes() as $code => $label): ?>
                <button
                    type="button"
                    class="modes-mode w-6 h-6 mode-<?= $escaper->escapeHtmlAttr(strtolower($code)) ?> <?= ($block->isModeActive($code)) ? 'active' : 'opacity-50 hover:opacity-100' ?>"
                    title="<?= $escaper->escapeHtmlAttr($label) ?>"
                    <?php if ($block->isModeActive($code)): ?>
                        disabled
                    <?php else: ?>
                        @click.prevent="changeUrl(
                            'product_list_mode',
                            '<?= $escaper->escapeHtmlAttr(strtolower($code)) ?>',
                            options.modeDefault
                        )"
                    <?php endif; ?>
                    data-role="mode-switcher"
                    data-value="<?= $escaper->escapeHtmlAttr(strtolower($code)) ?>"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Products view mode - %1', $label)) ?>"
                ></button>
            <?php endforeach; ?>
        </nav>
    <?php else: ?>
        <div class="modes w-20 order-1 col-span-1 sm:col-span-2 md:col-span-1 lg:col-span-2"></div>
    <?php endif; ?>
<?php endif; ?>
