<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use \Magento\LoginAsCustomerAssistance\ViewModel\ShoppingAssistanceViewModel;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ShoppingAssistanceViewModel $shoppingAssistanceViewModel */
$shoppingAssistanceViewModel = $viewModels->require(ShoppingAssistanceViewModel::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

if (!$shoppingAssistanceViewModel->isLoginAsCustomerEnabled()) {
    return;
}
?>
<script>
    function initAllowShippingAssistence() {
        return {
            isAllowed: <?= $shoppingAssistanceViewModel->isAssistanceAllowed() ? 'true' : 'false' ?>,
            showTooltip: false,
            onChange(value) {
                /**
                 * assistance_allowed accepts 2 for "allowed" and 1 for "not allowed"
                 * if assistance_allowed_checkbox is set to false, we cast it to 0 and add 1 === "1"
                 * if assistance_allowed_checkbox is set to true, we cast it to 1 and add 1 === "2"
                 */
                this.$refs['assistance-input'].value = Number(value) + 1;
            }
        }
    }
</script>
<div class="field choice"
     x-data="initAllowShippingAssistence()"
>
    <input type="checkbox"
           @change="onChange($event.target.checked)"
           name="assistance_allowed_checkbox"
           id="assistance_allowed_checkbox"
           value="1"
           title="<?= $escaper->escapeHtmlAttr(__('Allow remote shopping assistance')) ?>"
           class="checkbox"
           aria-describedby="assistance_allowed_checkbox_tooltip"
        <?php if ($shoppingAssistanceViewModel->isAssistanceAllowed()): ?>
            checked="checked"
        <?php endif; ?>
    />
    <label class="label text-cgrey-90 font-semibold" for="assistance_allowed_checkbox">
        <span><?= $escaper->escapeHtml(__($shoppingAssistanceViewModel->getAssistanceCheckboxTitle())) ?></span>
    </label>
    <input type="hidden"
           name="assistance_allowed"
           x-ref="assistance-input"
           value="<?= (int) $shoppingAssistanceViewModel->isAssistanceAllowed() + 1 ?>"
    />

    <div class="inline-flex px-4">
        <button
            type="button"
            @mouseover.prevent.stop="showTooltip = true"
            @mouseleave.prevent.stop="showTooltip = false"
            @focus="showTooltip = true"
            @focusout="showTooltip = false"
            @keydown.escape="showTooltip = false"
            aria-labelledby="assistance_allowed_checkbox_tooltip"
            aria-disabled="true"
        >
        <?= $heroicons->questionMarkCircleHtml('cursor-hand') ?>
        </button>
        <div class="relative" x-cloak x-show="showTooltip">
            <div class="shadow-lg">
                <div class="absolute top-0 left-0 z-10 w-64 p-2 -mt-6 text-sm leading-tight text-black transform
                    -translate-x-full md:-translate-x-1/3 -translate-y-full bg-white rounded-lg shadow-lg p-4">
                    <span
                        class="subtitle text-base"
                        id="assistance_allowed_checkbox_tooltip"
                    >
                        <?= $escaper->escapeHtml(__($shoppingAssistanceViewModel->getAssistanceCheckboxTooltip())) ?>
                    </span>
                </div>
                <svg class="absolute z-10 w-8 h-8 text-white transform -translate-x-full
                    -translate-y-8 fill-current stroke-current" width="12" height="12" aria-hidden="true">
                    <rect x="12" y="-12" width="12" height="12" transform="rotate(45)" class="shadow-xl" />
                </svg>
            </div>
        </div>
    </div>
</div>
