<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Advanced Search Hyva Compatibility M2 by Amasty
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductPrice;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);
/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
/** @var ProductPrice $productPrice */
$productPrice = $viewModels->require(ProductPrice::class);
// phpcs:disable Generic.Files.LineLength.TooLong
?>
<div class="w-full">
    <div class="flex row justify-between w-full mb-2 pb-4 p-2 items-center">
        <h3 class="text-lg font-bold">
           <span x-text="sections.products.title"></span> (<span x-text="sections.products.total_count"></span>)
        </h3>

        <div class="am-search-view-all" x-show="getResultProductsCount() >= 1">
            <a :href="resultPageUrl + latestQuery" class="text-blue-600 hover:text-blue-800 ">
                <?= $escaper->escapeHtml(__('View All')) ?> <span x-show="getResultProductsCount() > 0">(+<span x-text="getResultProductsCount()"></span>)</span>
            </a>
        </div>
    </div>

    <div class="relative flex flex-wrap w-full gap-2 px-2 md:px-0 mb-6 md:mb-0 am-search-results">
        <template x-for="(product, index) in (sections.products.items || [])" :key="index">
            <?= /** @noEscape  */ $block->fetchView(
                $block->getTemplateFile('Amasty_XsearchHyvaCompatibility::header-search/components/product.phtml')
            ) ?>
        </template>
    </div>
</div>
