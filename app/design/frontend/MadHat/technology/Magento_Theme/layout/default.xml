<?xml version="1.0"?>
<page layout="3columns" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <remove src="Magezon_Core::css/styles.css"/>
        <remove src="Magezon_Core::css/owlcarousel/owl.carousel.min.css"/>
        <remove src="Magezon_Core::css/animate.css"/>
        <remove src="Magezon_Core::css/mgz_font.css"/>
        <remove src="Magezon_Builder::css/openiconic.min.css"/>
        <remove src="Magezon_Builder::css/styles.css"/>
        <remove src="Magezon_Builder::css/common.css"/>
    </head>
    <body>
        <referenceContainer name="header.container">
            <block name="header_top_section"
                template="Magento_Theme::html/header-top-section.phtml"
                before="header-content"
            />
        </referenceContainer>   
        <referenceContainer name="head.additional">
            <referenceBlock name="font-awesome" remove="true"/>
        </referenceContainer>
        <referenceContainer name="footer" htmlClass="footer footer-content">
            <block name="footer-content" template="Magento_Theme::html/footer.phtml">
                <block class="Magento\Cms\Block\Block" name="footer-cms-content">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_4_columns</argument>
                    </arguments>
                </block>
               <block class="Magento\Cms\Block\Block" name="footer-cms-copyright">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_bottom</argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>
    </body>
</page>
