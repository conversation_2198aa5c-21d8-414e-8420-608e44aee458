<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

?>
<div x-data="initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?>()"
     class="z-20 order-1 md:order-2 lg:order-2 navigation hidden lg:flex flex-1"
>
    <!-- desktop -->
    <div x-ref="nav-desktop"
         @load.window="setActiveMenu($root)"
         class="hidden lg:block lg:relative lg:min-h-0 lg:px-8 lg:w-auto lg:pt-0">
        <nav
            class="duration-150 ease-in-out transform w-auto relative min-h-0 transition-display"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Main menu')) ?>"
        >
            <ul class="flex justify-start">
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li class="relative mr-2 level-0"
                        @mouseenter="hoverPanelActiveId = '<?= /* @noEscape */ (string) $index ?>'"
                        @mouseleave="hoverPanelActiveId = 0"
                        @keyup.escape="hoverPanelActiveId = 0"
                    >
                        <span class="flex items-center gap-1 p-3 text-md bg-opacity-95">
                            <a class="w-full py-3 text-base text-gray-700 hover:underline level-0"
                               href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                               title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                               @focus="hoverPanelActiveId = 0"
                            >
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    type="button"
                                    data-sr-button-id="<?= $escaper->escapeHtmlAttr($index) ?>"
                                    :aria-expanded="hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>' ? 'true' : 'false'"
                                    @click="openMenuOnClick('<?= /* @noEscape */ (string) $index ?>')"
                                >
                                    <?= $heroiconsSolid->chevronDownHtml("flex self-center h-5 w-5", 25, 25, ['aria-hidden' => 'true']) ?>
                                    <span class="sr-only">
                                        <?= $escaper->escapeHtml(__('Show submenu for %1 category', $menuItem['name'])) ?>
                                    </span>
                                </button>
                            <?php endif; ?>
                        </span>
                        <?php if (!empty($menuItem['childData'])): ?>
                            <ul
                                class="absolute z-10 hidden px-6 py-4 -ml-6 shadow-lg bg-container-lighter/95"
                                :class="{
                                    'hidden' : hoverPanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                    'block' : hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>'
                                }"
                            >
                                <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                    <li>
                                        <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                           title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                           class="block w-full px-3 py-1 my-1 whitespace-nowrap first:mt-0 hover:underline"
                                           @keyup.escape="$nextTick(() => document.querySelector('[data-sr-button-id=<?= $escaper->escapeJs($index) ?>]').focus())"
                                        >
                                            <span class="text-base text-gray-700">
                                                <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                            </span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
            <!-- Add custom menu items here -->
            <?php
            $objectManager = \Magento\Framework\App\ObjectManager::getInstance();

            /** @var VariableFactory $variableFactory */
            $variableModel = $objectManager->create(\Magento\Variable\Model\Variable::class);

            $fhrUrlSparesParts = $variableModel->loadByCode('fhr_url_spare_parts')->getPlainValue();
            $fhrUrlAccessories = $variableModel->loadByCode('fhr_url_accessories')->getPlainValue();
            $fhrUrlUsedPhones = $variableModel->loadByCode('fhr_url_used_phones')->getPlainValue();
            $fhrUrlBargainCorner = $variableModel->loadByCode('fhr_url_bargain_corner')->getPlainValue();
            ?>
            <ul>
                <li id="fhr-menu-custom-spares-parts" class="ui-menu-item level0" style="margin: 0;">
                    <a class="level-top" href="<?= $escaper->escapeUrl($fhrUrlSparesParts) ?>"><span><?= __('Spares parts') ?></span></a>
                </li>
                <li id="fhr-menu-custom-accessories" class="ui-menu-item level0" style="margin: 0;">
                    <a class="level-top" href="<?= $escaper->escapeUrl($fhrUrlAccessories) ?>"><span><?= __('Accessories') ?></span></a>
                </li>
                <li id="fhr-menu-custom-used-phones" class="ui-menu-item level0" style="margin: 0;">
                    <a class="level-top" href="<?= $escaper->escapeUrl($fhrUrlUsedPhones) ?>"><span><?= __('Used Phones') ?></span></a>
                </li>
                <li id="fhr-menu-custom-bargain-corner" class="ui-menu-item level0" style="margin: 0;">
                    <a class="level-top" href="<?= $escaper->escapeUrl($fhrUrlBargainCorner) ?>"><span><?= __('Bargain corner') ?></span></a>
                </li>
            </ul>
        </nav>
    </div>
</div>
<script>
    'use strict';

    const initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            hoverPanelActiveId: null,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                    item.closest('div.level-0') &&
                    item.closest('div.level-0').querySelector('a.level-0').classList.add('underline');
                });
            },
            openMenuOnClick(menuNode) {
                if (menuNode === this.hoverPanelActiveId) {
                    this.hoverPanelActiveId = 0;
                } else {
                    this.hoverPanelActiveId = menuNode
                }
            }
        }
    }
</script>
