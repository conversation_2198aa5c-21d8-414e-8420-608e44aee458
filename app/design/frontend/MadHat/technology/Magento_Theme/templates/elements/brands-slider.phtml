<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var Template $block */
/** @var Escaper $escaper */

$icons = $viewModels->require(SvgIcons::class);

/**
 * Each item needs either 'svg' or 'image_path' to be set
 * `svg` expects a valid string containing SVG source code,
 * `image_url` expects an url to an image
 *
 * We're generating the SVG source using the SvgIcons, which are documented here:
 * https://docs.hyva.io/hyva-themes/writing-code/working-with-view-models/svgicons.html
 */
$items = [
    [
        'url'          => $block->getUrl('home'), // optional
        'svg'          => $icons->renderHtml('lorum-logo-1', '', 170, 38)
    ],
    /* [
        // alternatively, define an image_url with additional data:
        'image_url'    => $block->getViewFileUrl('Hyva_Theme::svg/lorum-logo-1.svg'),
        'alt'          => 'logo ipsum',
        'image_width'  => 170,
        'image_height' => 38,
    ], */
    [
        'url'          => $block->getUrl('home'),
        'svg'          => $icons->renderHtml('lorum-logo-2', '', 170, 38)
    ],
    [
        'url'          => $block->getUrl('home'),
        'svg'          => $icons->renderHtml('lorum-logo-3', '', 170, 52)
    ],
    [
        'url'          => $block->getUrl('home'),
        'svg'          => $icons->renderHtml('lorum-logo-4', '', 170, 88)
    ],
    [
        'url'          => $block->getUrl('home'),
        'svg'          => $icons->renderHtml('lorum-logo-1', '', 170, 38)
    ],
    [
        'url'          => $block->getUrl('home'),
        'svg'          => $icons->renderHtml('lorum-logo-2', '', 170, 38)
    ],
];
$itemIndex = 1;
?>

<script>
    function carousel() {
        return {
            container: null,
            prev: null,
            next: null,
            first: null,
            visibleElements: null,
            speed: 3000, // set to 0 to disable
            slidePage: true, // set to false for sliding one item at the time, true for the whole visible set
            sliderTimer: null,
            init() {
                this.container = this.$refs.container
                this.first = this.container.firstElementChild;

                this.update();
                this.container.addEventListener('scroll', this.update.bind(this), {passive: true});
                if(this.speed > 0){
                    this.start();
                }
            },
            start() {
                this.sliderTimer = setInterval(() => this.scrollToNextOrFirst() , this.speed);
            },
            stop() {
                clearInterval(this.sliderTimer);
            },
            update() {
                const rect = this.container.getBoundingClientRect();

                this.visibleElements = Array.from(this.container.children).filter((child) => {
                    const childRect = child.getBoundingClientRect();
                    return childRect.left >= Math.floor(rect.left) && Math.floor(childRect.right) <= rect.right;
                });

                if (this.visibleElements.length > 0) {
                    this.prev = this.getPrevElement();
                    this.next = this.getNextElement();
                }
            },
            getPrevElement() {
                const sibling = this.visibleElements[0].previousElementSibling;
                return (sibling instanceof HTMLElement) ? sibling : null;
            },
            getNextElement() {
                const list = this.visibleElements;
                const lastElementIsVisible = !(list[list.length - 1].nextElementSibling instanceof HTMLElement);

                const sibling = (this.slidePage)
                    ? list[list.length - 1].nextElementSibling
                    : ((lastElementIsVisible)
                        ? null
                        : list[0].nextElementSibling);

                return (sibling instanceof HTMLElement) ? sibling : null;
            },
            scrollToNextOrFirst() {
                (this.next !== null)? this.scrollTo(this.next) : this.scrollTo(this.first);
            },
            scrollTo(element) {
                const current = this.container;
                if (!current || !element) return;
                const nextScrollPosition = element.offsetLeft;

                current.scroll({
                    left: nextScrollPosition,
                    behavior: 'smooth',
                });
            }
        };
    }
</script>

<div>
<span class="bg-cgrey-50 text-corange"></span>

    <div class="flex mx-auto items-center">
        <div x-data="carousel()"
             x-id="['carousel-end', 'carousel-desc']"
             role="group"
             class="relative overflow-hidden min-w-full"
             aria-roledescription="<?= $escaper->escapeHtml(__('Carousel')) ?>"
             aria-label="<?= $escaper->escapeHtml(__('Our favourite brands')) ?>"
             :aria-describedby="$id('carousel-desc')"
        >
            <span
                    class="sr-only"
                    :id="$id('carousel-desc')"
                    tabindex="-1"
            >
                <?= $escaper->escapeHtml(__('Navigating through the elements of the carousel is possible using the tab key. You can skip the carousel using the skip link.')) ?>
            </span>
            <a
                    :href="`#${$id('carousel-end')}`"
                    class="action skip sr-only focus:not-sr-only focus:absolute focus:z-30 focus:bg-white"
            >
                <?= $escaper->escapeHtml(__('Press to skip the carousel')) ?>
            </a>
            <div x-ref="container" class="snap gap-3 flex overflow-x-scroll">
                <?php foreach ($items as $item): ?>
                    <div class="slide flex-auto grow-0 shrink-0 w-[calc(50%-6px)] flex
                                justify-center items-center md:w-[calc(25%-9px)]"
                         aria-roledescription="<?= $escaper->escapeHtml(__('Carousel item')) ?>"
                         :aria-hidden="!visibleElements.includes($el)"
                         aria-label="<?= $escaper->escapeHtmlAttr(__('Item %1 of %2', $itemIndex++, count($items))) ?>"
                         @focusin="stop()"
                    >
                        <?php if ($item['url'] ?? false): ?>
                            <a href="<?= $escaper->escapeUrl($item['url'])?>">
                        <?php endif; ?>

                        <?php if ($item['svg'] ?? false): ?>
                            <?= $item['svg'] ?>
                        <?php elseif ($item['image_url'] ?? false): ?>
                            <img src="<?= $escaper->escapeHtmlAttr($item['image_url']) ?>"
                                 alt="<?= $escaper->escapeHtmlAttr($item['alt'] ?? '') ?>"
                                 width="<?= $escaper->escapeHtmlAttr($item['image_width'] ?? '') ?>"
                                 height="<?= $escaper->escapeHtmlAttr($item['image_height'] ?? '') ?>"
                                 loading="lazy"
                            />
                        <?php endif; ?>

                        <?php if ($item['url'] ?? false): ?>
                            </a>
                        <?php endif; ?>

                    </div>
                <?php endforeach; ?>
            </div>
            <span :id="$id('carousel-end')" tabindex="-1"></span>
        </div>
    </div>
</div>
