<?php
declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Slider;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\StoreConfig;

/** @var ViewModelRegistry $viewModels */
/** @var Slider $sliderViewModel */
/** @var Template $block */
/** @var StoreConfig $storeConfig */

$sliderViewModel = $viewModels->require(Slider::class);
$storeConfig = $viewModels->require(StoreConfig::class);

$sliderShowArrows = $block->getShowArrows() !== null ? $block->getShowArrows() : "end";
$sliderShowDots = $block->getShowDots() !== null ? (bool) $block->getShowDots() : 'true';
$loadFirstEager = (bool) $block->getLoadFirstEager();
$sliderId = (int) $block->getSliderId();
$imageWidth = (int) $block->getImageWidth() !== null ? (int) $block->getImageWidth()   : 1280;
$imageHeight = (int) $block->getImageHeight() !== null ? (int) $block->getImageHeight() : 853;

if ($sliderId) {
/** @var \Mageplaza\BannerSlider\Helper\Data $helperData */
$helperData = \Magento\Framework\App\ObjectManager::getInstance()->get(\Mageplaza\BannerSlider\Helper\Data::class);
$sliderId = 1; // Change this to the ID of your slider
$collection = $helperData->getBannerCollection($sliderId)->addFieldToFilter('status', 1);

// Get base URL
$baseUrlMedia = rtrim($block->getBaseUrl(), '/') . '/'.\Magento\Framework\UrlInterface::URL_TYPE_MEDIA . '/';
$items = [];
foreach ($collection as $banner) {
    $imageUrl = str_replace($baseUrlMedia, '', $banner->getImageUrl());
    $items[] = [
        'image_url'    => $imageUrl,
        'mobile_image_url'    => $imageUrl, // Assuming same image for mobile, adjust if different
        'image_width'  => 1280, // Adjust as necessary
        'image_height' => 853,  // Adjust as necessary
        'text_small'   => $banner->getTitle(),
        'text_large'   => $banner->getContent(),
        'url'          => $banner->getUrlBanner(),
    ];
}

?>
<div class="homepage-slier-container">
    <?=
    $sliderViewModel
        ->getSliderForItems(
            'Magento_Theme::elements/ui-slider/slider-item.phtml',
            $items,
            'Magento_Theme::elements/ui-slider/slider-php.phtml'
        )
        ->setShowArrows($sliderShowArrows)
        ->setShowDots($sliderShowDots)
        ->setLoadFirstEager($loadFirstEager)
        ->toHtml()
    ?>
</div>
<?php } else { echo __('Please set slider_id like slider_id="1" in block'); }?>
