<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Request a Quote Hyva Compatibility
 */

use Amasty\RequestQuote\Block\Cart\Sidebar;
use Amasty\RequestQuoteHyva\ViewModel\QuoteIcons;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;

/** @var Sidebar $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

$isLoggedIn = $block->isLoggedIn();
if (!$isLoggedIn) {
    return;
}

$quoteIcons = $viewModels->require(QuoteIcons::class);
?>

<a x-data="initAmQuoteIcon()"
   @click.prevent.stop="$dispatch('toggle-amquote',{})"
   @private-content-loaded.window="getData($event.detail.data)"
   id="amquote-icon"
   href="<?= $escaper->escapeUrl($block->getQuoteCartUrl()) ?>"
   class="hidden relative inline-block no-underline ml-3 hover:text-black">
    <span class="sr-only label">
        <?= $escaper->escapeHtml(__('Quote')) ?>
    </span>

    <?= $quoteIcons->quoteHtml(
        'w-5 h-5 md:h-6 md:w-6 hover:text-black',
        25,
        25
    ) ?>

    <span x-text="quotecart.summary_count"
          class="absolute top-0 right-0 hidden h-5 px-2 py-1 -mt-5 -mr-4 text-xs font-semibold
                        leading-none text-center text-white uppercase transform -translate-x-1
                        translate-y-1/2 rounded-full bg-primary"
          :class="{
                        'hidden': !quotecart.summary_count,
                        'block': quotecart.summary_count }"
    ></span>
</a>

<script>
    'use strict';

    (function () {
        document.addEventListener('DOMContentLoaded', function () {
            const quoteIcon = document.getElementById("amquote-icon");
            const insertBeforeNode = document.querySelector('<?= $escaper->escapeJs($block->getData('insertBeforeSelector')) ?>');
            if (!insertBeforeNode) {
                return;
            }

            const destination = insertBeforeNode.parentNode;
            destination.insertBefore(quoteIcon, insertBeforeNode);
            quoteIcon.classList.remove('hidden');
        });
    })()
</script>
<script>
    'use strict';

    function initAmQuoteIcon() {
        return {
            quotecart: {},
            getData(data) {
                if (data.quotecart) { this.quotecart = data.quotecart }
            }
        }
    }
</script>

<?= $block->getChildHtml('quotecart-drawer') ?>
