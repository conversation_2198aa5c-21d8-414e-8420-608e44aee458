<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Request a Quote Hyva Compatibility
 */

use Amasty\RequestQuote\Block\Cart\Item\Price\Renderer;
use Magento\Framework\Escaper;

/** @var Renderer $block */
/** @var Escaper $escaper */


$item = $block->getItem();
?>
<?php if ($block->canCustomizePrice()): ?>
    <div class="field price">
        <div class="control price flex lg:justify-end">
            <div class="flex flex-row relative request-quote-price">
            <label class="label" for="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-price"><?= $escaper->escapeHtmlAttr(__('Price')) ?></label>
                <span class="flex items-center mr-2 text-lg">
                    <?= $escaper->escapeHtml($block->getCurrencySymbol()) ?>
                </span>
                <input id="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-price"
                    name="cart[<?= $escaper->escapeHtmlAttr($item->getId()) ?>][price]"
                    value="<?= $escaper->escapeHtmlAttr($block->getInputPrice()) ?>"
                    type="number"
                    size="4"
                    title="<?= $escaper->escapeHtmlAttr(__('Price')) ?>"
                    class="form-input price w-28 text-center"
                    required
                    min="0"
                    step=".01"/>
            </div>
        </div>
    </div>
<?php else: ?>
    <?php if ((float)$item->getPrice()): ?>
        <?php if ($block->displayCartPriceInclTax()): ?>
            <?php $_incl = $block->getCheckoutHelper()->getPriceInclTax($item); ?>
            <span class="cart-price">
                <?= $escaper->escapeHtml($block->formatPrice($_incl), ['span']) ?>
            </span>
        <?php endif; ?>
        <?php if ($block->displayCartPriceExclTax()): ?>
            <span class="price-including-tax" data-label="<?= $escaper->escapeHtmlAttr(__('Excl. Tax')) ?>">
                <span class="cart-price">
                    <?= $escaper->escapeHtml($block->formatPrice($item->getCalculationPrice()), ['span']) ?>
                </span>
            </span>
        <?php endif; ?>
    <?php else: ?>
        <span class="cart-price">
            <?= $escaper->escapeHtml(__('N/A')) ?>
        </span>
    <?php endif; ?>
<?php endif; ?>
