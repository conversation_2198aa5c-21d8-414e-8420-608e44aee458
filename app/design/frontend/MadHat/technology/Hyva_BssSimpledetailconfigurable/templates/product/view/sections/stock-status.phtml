<?php
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category  BSS
 * @package   Hyva_BssSimpledetailconfigurable
 * <AUTHOR> Team
 * @copyright Copyright (c) 2023-present BSS Commerce Co. ( http://bsscommerce.com )
 * @license   http://bsscommerce.com/Bss-Commerce-License.txt
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\BssSimpledetailconfigurable\ViewModel\Helper as ModuleViewModel;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var Product $product */
$product = $this->hasData('product')
    ? $this->getData('product')
    : $currentProduct->get();

$moduleViewModel = $viewModels->require(ModuleViewModel::class);
$moduleConfig = $moduleViewModel->getModuleConfig();
if (!$product || !$product->getId()) {
    return;
}
$inStock = __('In stock');
$outOfStock = __('Out of stock');
$product->getTypeId();
?>
<?php if ($block->getParentBlock()->displayProductStockStatus()): ?>
    <div class="text-right">
        <?php if ($product->getTypeId() == 'configurable' && $moduleConfig->isShowName() && $moduleConfig->isModuleEnable()): ?>
            <p class="flex items-center justify-end align-middle available gap-x-2 stock"
               :class="{ 'available': stock_status, 'unavailable': !stock_status }"
               title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>"
               x-data="{stock_status: <?= $product->getIsSalable() ?>}">
                <span class="w-3 h-3 rounded-full shrink-0"
                      :class="{ 'bg-green-500': stock_status, 'bg-red-500': !stock_status }"></span>
                <span @simple-detail-product-active.window="stock_status = $event.detail.product.stock_status"
                      x-text=" stock_status ? '<?= $inStock; ?>' : '<?= $outOfStock; ?>'"></span>
            </p>
        <?php else: ?>
            <?php if ($product->getIsSalable()): ?>
                <p class="flex items-center justify-end align-middle available gap-x-2 stock"
                   title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                    <span class="w-3 h-3 bg-green-500 rounded-full shrink-0"></span>
                    <span>
                        <?php $madhatInventoryStatus = (string) $product->getAttributeText('madhat_inventory_status'); ?>
                        <?= $madhatInventoryStatus; ?>
                        <?php //= $escaper->escapeHtml(__('In stock')) ?>
                    </span>
                </p>
            <?php else: ?>
                <p class="flex items-center justify-end align-middle gap-x-2 unavailable stock"
                   title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                    <span class="w-3 h-3 bg-red-500 rounded-full shrink-0"></span>
                    <span>
                        <?php $madhatInventoryStatus = (string) $product->getAttributeText('madhat_inventory_status'); ?>
                        <?= $madhatInventoryStatus; ?>
                        <?php //= $escaper->escapeHtml(__('Out of stock')) ?>
                    </span>
                </p>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endif; ?>

