#!/bin/bash

echo "Executing validate.sh script..."

echo "SSH_HOSTNAME:$SSH_HOSTNAME"
echo "SSH_USERNAME:$SSH_USERNAME"
echo "TEST1_HOSTNAME:$TEST1_HOSTNAME"
echo "TEST1_USERNAME:$TEST1_USERNAME"
echo "STAGE1_HOSTNAME:$STAGE1_HOSTNAME"
echo "STAGE1_USERNAME:$STAGE1_USERNAME"
echo "PROD1_HOSTNAME:$PROD1_HOSTNAME"
echo "PROD1_USERNAME:$PROD1_USERNAME"

echo "SUCCESS" > .job_status

if [ -z "$SSH_HOSTNAME" ] || [ -z "$SSH_USERNAME" ]; then
    echo "SSH_HOSTNAME, SSH_USERNAME are required"
    echo "" > .job_status
    exit 1
fi
if [ -z "$MAGENTO_ROOT_DIR" ]; then
    echo "MAGENTO_ROOT_DIR is required"
    echo "" > .job_status
    exit 1
fi
if [ -z "$REDIS_HOSTNAME" ] || [ -z "$REDIS_USERNAME" ]; then
    echo "REDIS_HOSTNAME, REDIS_USERNAME are required"
    echo "" > .job_status
    exit 1
fi

echo "Finished validate.sh script"