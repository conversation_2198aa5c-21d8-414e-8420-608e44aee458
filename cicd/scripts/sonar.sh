#!/bin/bash
echo "Running sonar.sh ..."
echo "Working directory: " `pwd`

echo "Downloading sonar scanner from Artifactory ..."
wget -nv -O sonarscanner.zip http://artifactory.spc.se/artifactory/ext-release-local/org/sonarsource/scanner/sonar-scanner-cli-4.7.0.2747-linux.zip

echo "Expanding sonar scanner ..."
mkdir -p sonar
unzip sonarscanner.zip -d sonar

echo "Copying sonar config ..."
#cp cicd/scripts/sonar-project.properties sonar/sonar-scanner-4.7.0.2747-linux/conf
cp cicd/scripts/sonar-project.properties .

echo "Adjusting PATH ..."
PATH=$PATH:`pwd`/sonar/sonar-scanner-4.7.0.2747-linux/bin

echo "Triggering sonar scanner ..."
sonar-scanner -X

echo "Exiting sonar.sh"

# Download sonar scanner
# http://artifactory.spc.se/artifactory/ext-release-local/org/sonarsource/scanner/sonar-scanner-cli-4.7.0.2747-linux.zip
# Follow the rest from https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/
