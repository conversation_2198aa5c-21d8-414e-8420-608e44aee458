# ./docker-compose.eduardas.yml for "fhr" project

services:
  fhr-db:
    hostname: db.fhr.docker
    image: 'mariadb:10.6'
    shm_size: 2gb
    mem_limit: 4g
    memswap_limit: 5g
    environment:
      - MYSQL_ROOT_PASSWORD=magento2
      - MYSQL_DATABASE=magento2
      - MYSQL_USER=magento2
      - MYSQL_PASSWORD=magento2
    ports:
      - '3306:3306' # Changed port to avoid conflict
    volumes:
      - '.:/app:delegated'
      - 'fhr-magento-db:/var/lib/mysql'
      - './docker-mariadb/my.cnf:/etc/mysql/my.cnf' # Mount your custom my.cnf
      - './docker-mariadb/backups:/var/backups'
      - './docker-mariadb/init-mysql.sql:/docker-entrypoint-initdb.d/init-mysql.sql' # Mount the init script
    healthcheck:
      test: 'mysqladmin ping -h localhost -pmagento2'
      interval: 30s
      timeout: 30s
      retries: 3
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - db.fhr.docker

  fhr-redis:
    hostname: redis.fhr.docker
    image: 'redis:6.0.16'
    volumes:
      - '.:/app:delegated'
    ports:
      - '6379:6379' # Changed port to avoid conflict
    sysctls:
      net.core.somaxconn: 1024
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000
    healthcheck:
      test: 'redis-cli ping || exit 1'
      interval: 30s
      timeout: 30s
      retries: 3
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - redis.fhr.docker

  fhr-elasticsearch:
    hostname: elasticsearch.fhr.docker
    image: 'magento/magento-cloud-docker-elasticsearch:7.10-1.3.6'
    environment:
      - cluster.name=docker-cluster
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - '9200:9200'
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - elasticsearch.fhr.docker

  fhr-fpm:
    hostname: fpm.fhr.docker
#    image: 'php:8.2-fpm'  # Updated PHP version to 8.2
    image: 'php:8.1-fpm'  # Updated PHP version to 8.1
    build:
      context: ./docker-php-fpm
    volumes:
      - '.:/app:delegated'
      - './:/var/www/html:delegated'  # Adjusted volume mapping
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - fpm.fhr.docker
    depends_on:
      fhr-db:
        condition: service_healthy

  fhr-web:
    hostname: web.fhr.docker
    image: 'nginx:1.18'
    build:
      context: ./docker-nginx
    volumes:
      - '.:/var/www/html:delegated'
    environment:
      - WITH_XDEBUG=0
      - NGINX_WORKER_PROCESSES=1
      - NGINX_WORKER_CONNECTIONS=1024
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - web.fhr.docker
    depends_on:
      fhr-fpm:
        condition: service_started

  fhr-varnish:
    hostname: varnish.fhr.docker
    image: 'magento/magento-cloud-docker-varnish:6.6-1.3.6'
    networks:
      fhr:
        ipv4_address: ***********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - varnish.fhr.docker
    depends_on:
      fhr-web:
        condition: service_started
    volumes:
      - './docker-varnish/default.vcl:/etc/varnish/default.vcl:ro'  # Mount your default.vcl file here

  fhr-tls:
    hostname: tls.fhr.docker
    image: 'nginx:1.18'
    build:
      context: ./docker-nginx
    volumes:
      - '.:/var/www/html:delegated'
    networks:
      fhr:
        ipv4_address: ***********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - fhr.docker
    environment:
      - NGINX_WORKER_PROCESSES=1
      - NGINX_WORKER_CONNECTIONS=1024
      - UPSTREAM_HOST=varnish
      - UPSTREAM_PORT=80
    ports:
      - '80:80'  # Changed ports to avoid conflict
      - '443:443' # Changed ports to avoid conflict
    depends_on:
      fhr-varnish:
        condition: service_started

  fhr-rabbitmq:
    hostname: rabbitmq.fhr.docker
    image: 'rabbitmq:3.13.3-management'
    ports:
      - '5672:5672'  # Changed ports to avoid conflict
      - '15672:15672' # Changed ports to avoid conflict
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - rabbitmq.fhr.docker
    command: >
      sh -c '
        rabbitmq-plugins enable rabbitmq_management;
        rabbitmq-server
      '

  fhr-mailhog:
    hostname: mailhog.fhr.docker
    image: 'mailhog/mailhog:latest'
    ports:
      - '1025:1025'  # Changed ports to avoid conflict
      - '8025:8025'  # Changed ports to avoid conflict
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - mailhog.fhr.docker

  fhr-phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      - PMA_HOST=fhr-db
      - PMA_USER=magento2
      - PMA_PASSWORD=magento2
      - PMA_PORT=3306
    ports:
      - '8080:80'
    depends_on:
      - fhr-db
    networks:
      fhr:
        ipv4_address: **********   # Specify a unique IPv4 address within the custom subnet
        aliases:
          - phpmyadmin.fhr.docker

volumes:
  fhr-magento-db: {  }

networks:
  fhr:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16   # Define a custom subnet for the fhr network
